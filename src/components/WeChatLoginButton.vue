<script setup lang="ts">
import { ref } from 'vue'
import { useEnhancedUserStore } from '@/stores/enhanced-user'

// Props
interface Props {
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<Emits>()

// Emits
interface Emits {
  (e: 'login-success'): void
  (e: 'login-error', error: string): void
  (e: 'login-start'): void
}

// Store
const userStore = useEnhancedUserStore()

// 内部状态
const isLogging = ref(false)

// 计算属性
const loading = computed(() => props.loading || isLogging.value || userStore.isLogging)

// 微信登录处理
async function handleLogin() {
  if (loading.value) { return }

  try {
    isLogging.value = true
    emit('login-start')

    // 检查微信环境
    if (!checkWeChatEnvironment()) {
      throw new Error('请在微信小程序中使用')
    }

    // 执行微信登录
    const success = await userStore.wxLogin()

    if (success) {
      emit('login-success')
    }
    else {
      throw new Error('登录失败，请重试')
    }
  }
  catch (error: any) {
    console.error('微信登录失败:', error)

    let errorMessage = '登录失败，请重试'

    // 根据错误类型提供更具体的错误信息
    if (error.message) {
      if (error.message.includes('用户拒绝')) {
        errorMessage = '用户取消授权'
      }
      else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络'
      }
      else if (error.message.includes('微信')) {
        errorMessage = '微信授权失败，请重试'
      }
      else {
        errorMessage = error.message
      }
    }

    emit('login-error', errorMessage)
  }
  finally {
    isLogging.value = false
  }
}

// 检查微信环境
function checkWeChatEnvironment(): boolean {
  // #ifdef MP-WEIXIN
  return true
  // #endif

  // #ifndef MP-WEIXIN
  console.warn('当前不在微信小程序环境中')
  return false
  // #endif
}

// 获取用户授权
function getUserAuthorization(): Promise<any> {
  return new Promise((resolve, reject) => {
    uni.authorize({
      scope: 'scope.userInfo',
      success: resolve,
      fail: (error) => {
        // 如果用户之前拒绝过授权，引导用户到设置页面
        if (error.errMsg.includes('deny')) {
          uni.showModal({
            title: '授权提示',
            content: '需要获取您的微信用户信息，请在设置中开启授权',
            confirmText: '去设置',
            success: (modalRes) => {
              if (modalRes.confirm) {
                uni.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.userInfo']) {
                      resolve(settingRes)
                    }
                    else {
                      reject(new Error('用户拒绝授权'))
                    }
                  },
                  fail: reject,
                })
              }
              else {
                reject(new Error('用户取消授权'))
              }
            },
          })
        }
        else {
          reject(error)
        }
      },
    })
  })
}

// 暴露方法给父组件
defineExpose({
  handleLogin,
})
</script>

<template>
  <button
    class="wechat-login-btn flex w-full items-center justify-center space-x-3 rounded-xl bg-green-500 px-6 py-4 font-semibold text-white shadow-lg transition-all duration-200 hover:bg-green-600"
    :class="{
      'cursor-not-allowed opacity-50': loading,
      'active:scale-95': !loading,
    }"
    :disabled="loading"
    @click="handleLogin"
  >
    <!-- 微信图标 -->
    <view class="flex size-6 items-center justify-center">
      <text v-if="!loading" class="text-xl">💬</text>
      <view v-else class="loading-spinner size-5 animate-spin rounded-full border-2 border-white border-t-transparent" />
    </view>

    <!-- 按钮文字 -->
    <text class="text-base font-medium">
      {{ loading ? '登录中...' : '微信授权登录' }}
    </text>
  </button>
</template>

<style scoped>
.wechat-login-btn {
  background: linear-gradient(135deg, #07c160 0%, #00ae42 100%);
  box-shadow: 0 4px 15px rgba(7, 193, 96, 0.3);
  border: none;
  outline: none;
}

.wechat-login-btn:hover {
  background: linear-gradient(135deg, #06b050 0%, #009938 100%);
  box-shadow: 0 6px 20px rgba(7, 193, 96, 0.4);
  transform: translateY(-1px);
}

.wechat-login-btn:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 10px rgba(7, 193, 96, 0.3);
}

.wechat-login-btn:disabled {
  background: #a0a0a0;
  box-shadow: none;
  transform: none;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 微信绿色主题 */
.wechat-login-btn {
  position: relative;
  overflow: hidden;
}

.wechat-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.wechat-login-btn:hover::before {
  left: 100%;
}
</style>

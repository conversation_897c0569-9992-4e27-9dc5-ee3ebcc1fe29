/**
 * 数据层测试工具
 * 用于验证 Phase 2 数据层功能
 */

import { memoryStorage } from '@/services/storage'
import { apiClient, authApi, entryApi, emotionApi, causeApi } from '@/services/api'
import { useEnhancedUserStore, useEnhancedEmotionsStore } from '@/stores'
import type { 
  WxLoginRequest, 
  CreateEntryRequest, 
  QueryEntriesRequest,
  User,
  Entry,
  Emotion,
  Cause
} from '@/types'

/**
 * 测试内存存储服务
 */
export const testMemoryStorage = async () => {
  console.log('=== 测试内存存储服务 ===')
  
  try {
    // 清空存储
    memoryStorage.clear()
    
    // 测试用户操作
    const testUser = await memoryStorage.createUser({
      open_id: 'test_openid_001',
      username: '测试用户',
      avatar_url: 'https://example.com/avatar.jpg',
      last_login_time: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    
    console.log('创建用户成功:', testUser)
    
    // 测试用户查询
    const foundUser = await memoryStorage.getUserById(testUser.user_id)
    console.log('查询用户成功:', foundUser)
    
    const foundByOpenId = await memoryStorage.getUserByOpenId(testUser.open_id)
    console.log('通过OpenID查询用户成功:', foundByOpenId)
    
    // 测试情绪记录操作
    const emotions = await memoryStorage.getEmotions()
    const causes = await memoryStorage.getCauses()
    
    console.log('情绪数量:', emotions.length)
    console.log('原因数量:', causes.length)
    
    if (emotions.length > 0 && causes.length > 0) {
      // 创建测试记录
      const testEntry = await memoryStorage.createEntry({
        user_id: testUser.user_id,
        emotion_id: emotions[0].emotion_id,
        cause_id: causes[0].cause_id,
        intensity: 4,
        notes: '这是一条测试记录',
        date: new Date().toISOString()
      })
      
      console.log('创建记录成功:', testEntry)
      
      // 查询记录
      const queryResult = await memoryStorage.queryEntries({
        user_id: testUser.user_id,
        page: 1,
        page_size: 10
      })
      
      console.log('查询记录成功:', queryResult)
      
      // 更新记录
      const updatedEntry = await memoryStorage.updateEntry(testEntry.entry_id, {
        notes: '更新后的笔记内容',
        intensity: 5
      })
      
      console.log('更新记录成功:', updatedEntry)
      
      // 删除记录
      const deleteResult = await memoryStorage.deleteEntry(testEntry.entry_id)
      console.log('删除记录成功:', deleteResult)
    }
    
    return {
      success: true,
      message: '内存存储服务测试通过'
    }
  } catch (error) {
    console.error('内存存储服务测试失败:', error)
    return {
      success: false,
      message: `内存存储服务测试失败: ${error}`
    }
  }
}

/**
 * 测试 API 服务层
 */
export const testApiService = async () => {
  console.log('=== 测试 API 服务层 ===')
  
  try {
    // 测试微信登录 API
    const loginRequest: WxLoginRequest = {
      code: 'test_wx_code_001',
      user_info: {
        nick_name: 'API测试用户',
        avatar_url: 'https://example.com/api-avatar.jpg',
        gender: 1,
        country: '中国',
        province: '北京',
        city: '北京',
        language: 'zh_CN'
      }
    }
    
    const loginResponse = await authApi.wxLogin(loginRequest)
    console.log('微信登录 API 测试成功:', loginResponse)
    
    // 设置访问令牌
    apiClient.setAccessToken(loginResponse.data.access_token)
    
    // 测试情绪列表 API
    const emotionsResponse = await emotionApi.getAll()
    console.log('情绪列表 API 测试成功:', emotionsResponse.data.length, '个情绪')
    
    // 测试原因列表 API
    const causesResponse = await causeApi.getAll()
    console.log('原因列表 API 测试成功:', causesResponse.data.length, '个原因')
    
    // 测试创建记录 API
    if (emotionsResponse.data.length > 0 && causesResponse.data.length > 0) {
      const createEntryRequest: CreateEntryRequest = {
        emotion_id: emotionsResponse.data[0].emotion_id,
        cause_id: causesResponse.data[0].cause_id,
        intensity: 3,
        notes: 'API 测试记录',
        date: new Date().toISOString()
      }
      
      const createResponse = await entryApi.create(createEntryRequest)
      console.log('创建记录 API 测试成功:', createResponse.data)
      
      // 测试查询记录 API
      const queryRequest: QueryEntriesRequest = {
        page: 1,
        page_size: 10
      }
      
      const queryResponse = await entryApi.query(queryRequest)
      console.log('查询记录 API 测试成功:', queryResponse.data.total, '条记录')
      
      // 测试更新记录 API
      const updateResponse = await entryApi.update(createResponse.data.entry_id, {
        entry_id: createResponse.data.entry_id,
        notes: 'API 更新后的记录'
      })
      console.log('更新记录 API 测试成功:', updateResponse.data)
      
      // 测试删除记录 API
      const deleteResponse = await entryApi.delete(createResponse.data.entry_id)
      console.log('删除记录 API 测试成功:', deleteResponse.data)
    }
    
    return {
      success: true,
      message: 'API 服务层测试通过'
    }
  } catch (error) {
    console.error('API 服务层测试失败:', error)
    return {
      success: false,
      message: `API 服务层测试失败: ${error}`
    }
  }
}

/**
 * 测试增强版 Stores
 */
export const testEnhancedStores = async () => {
  console.log('=== 测试增强版 Stores ===')
  
  try {
    const userStore = useEnhancedUserStore()
    const emotionsStore = useEnhancedEmotionsStore()
    
    // 初始化 stores
    userStore.init()
    emotionsStore.init()
    
    console.log('Stores 初始化完成')
    
    // 测试用户登录
    const loginSuccess = await userStore.wxLogin()
    console.log('用户登录测试:', loginSuccess ? '成功' : '失败')
    
    if (loginSuccess) {
      console.log('当前用户:', userStore.userName)
      console.log('登录状态:', userStore.isLoggedIn)
      
      // 等待情绪和原因数据加载
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 测试情绪记录
      if (emotionsStore.emotions.length > 0 && emotionsStore.causes.length > 0) {
        // 更新表单
        emotionsStore.updateCurrentForm({
          emotion_id: emotionsStore.emotions[0].emotion_id,
          cause_id: emotionsStore.causes[0].cause_id,
          intensity: 4,
          notes: '增强版 Store 测试记录'
        })
        
        console.log('当前表单:', emotionsStore.currentForm)
        
        // 验证表单
        const validation = emotionsStore.validateForm(emotionsStore.currentForm)
        console.log('表单验证结果:', validation)
        
        if (validation.isValid) {
          // 添加记录
          const newEntry = await emotionsStore.addEntry(
            emotionsStore.currentForm, 
            userStore.userId
          )
          console.log('添加记录成功:', newEntry)
          
          console.log('今日记录数:', emotionsStore.todayEntries.length)
          console.log('总记录数:', emotionsStore.entries.length)
          
          // 测试更新记录
          await emotionsStore.updateEntry(newEntry.entry_id, {
            notes: '更新后的记录内容'
          })
          console.log('更新记录成功')
          
          // 测试删除记录
          await emotionsStore.deleteEntry(newEntry.entry_id)
          console.log('删除记录成功')
        }
      }
      
      // 测试设置更新
      await userStore.updateSettings({
        theme: 'dark',
        notifications_enabled: false
      })
      console.log('设置更新成功:', userStore.appSettings)
      
      // 测试同步状态
      console.log('用户同步状态:', userStore.syncStatus)
      console.log('情绪同步状态:', emotionsStore.syncStatus)
      console.log('待同步数据:', userStore.hasPendingSync, emotionsStore.hasPendingSync)
    }
    
    return {
      success: true,
      message: '增强版 Stores 测试通过'
    }
  } catch (error) {
    console.error('增强版 Stores 测试失败:', error)
    return {
      success: false,
      message: `增强版 Stores 测试失败: ${error}`
    }
  }
}

/**
 * 测试数据层集成
 */
export const testDataLayerIntegration = async () => {
  console.log('=== 测试数据层集成 ===')
  
  try {
    // 测试存储 → API → Store 数据流
    console.log('1. 测试存储层...')
    const storageResult = await testMemoryStorage()
    
    console.log('2. 测试 API 层...')
    const apiResult = await testApiService()
    
    console.log('3. 测试 Store 层...')
    const storeResult = await testEnhancedStores()
    
    const allPassed = storageResult.success && apiResult.success && storeResult.success
    
    console.log('📊 数据层集成测试结果:')
    console.log(`存储层: ${storageResult.success ? '✅' : '❌'} ${storageResult.message}`)
    console.log(`API层: ${apiResult.success ? '✅' : '❌'} ${apiResult.message}`)
    console.log(`Store层: ${storeResult.success ? '✅' : '❌'} ${storeResult.message}`)
    
    if (allPassed) {
      console.log('🎉 数据层集成测试全部通过！')
      uni.showToast({
        title: '数据层测试通过！',
        icon: 'success'
      })
    } else {
      console.log('⚠️ 部分数据层测试失败')
      uni.showToast({
        title: '部分测试失败',
        icon: 'none'
      })
    }
    
    return {
      allPassed,
      results: {
        storage: storageResult,
        api: apiResult,
        store: storeResult
      }
    }
  } catch (error) {
    console.error('数据层集成测试失败:', error)
    return {
      allPassed: false,
      error: error
    }
  }
}

/**
 * 性能测试
 */
export const testPerformance = async () => {
  console.log('=== 性能测试 ===')
  
  const startTime = Date.now()
  
  try {
    // 批量创建记录测试
    const batchSize = 100
    const emotions = await memoryStorage.getEmotions()
    const causes = await memoryStorage.getCauses()
    
    if (emotions.length === 0 || causes.length === 0) {
      throw new Error('没有可用的情绪或原因数据')
    }
    
    console.log(`开始批量创建 ${batchSize} 条记录...`)
    const batchStartTime = Date.now()
    
    const promises = []
    for (let i = 0; i < batchSize; i++) {
      const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)]
      const randomCause = causes[Math.floor(Math.random() * causes.length)]
      
      promises.push(memoryStorage.createEntry({
        user_id: 'perf_test_user',
        emotion_id: randomEmotion.emotion_id,
        cause_id: randomCause.cause_id,
        intensity: Math.floor(Math.random() * 5) + 1,
        notes: `性能测试记录 ${i + 1}`,
        date: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      }))
    }
    
    await Promise.all(promises)
    const batchEndTime = Date.now()
    
    console.log(`批量创建完成，耗时: ${batchEndTime - batchStartTime}ms`)
    console.log(`平均每条记录: ${(batchEndTime - batchStartTime) / batchSize}ms`)
    
    // 查询性能测试
    console.log('开始查询性能测试...')
    const queryStartTime = Date.now()
    
    const queryResult = await memoryStorage.queryEntries({
      user_id: 'perf_test_user',
      page: 1,
      page_size: 50,
      sort_by: 'created_at',
      sort_order: 'desc'
    })
    
    const queryEndTime = Date.now()
    
    console.log(`查询完成，耗时: ${queryEndTime - queryStartTime}ms`)
    console.log(`查询到 ${queryResult.items.length} 条记录`)
    
    const totalTime = Date.now() - startTime
    
    return {
      success: true,
      metrics: {
        totalTime,
        batchCreateTime: batchEndTime - batchStartTime,
        avgCreateTime: (batchEndTime - batchStartTime) / batchSize,
        queryTime: queryEndTime - queryStartTime,
        recordsCreated: batchSize,
        recordsQueried: queryResult.items.length
      }
    }
  } catch (error) {
    console.error('性能测试失败:', error)
    return {
      success: false,
      error: error
    }
  }
}

/**
 * 运行所有数据层测试
 */
export const runAllDataLayerTests = async () => {
  console.log('🧪 开始运行数据层测试套件...')
  
  const startTime = Date.now()
  
  try {
    // 运行集成测试
    const integrationResult = await testDataLayerIntegration()
    
    // 运行性能测试
    const performanceResult = await testPerformance()
    
    const endTime = Date.now()
    const totalTime = endTime - startTime
    
    console.log('📊 测试套件完成，总耗时:', totalTime, 'ms')
    
    return {
      success: integrationResult.allPassed && performanceResult.success,
      integration: integrationResult,
      performance: performanceResult,
      totalTime
    }
  } catch (error) {
    console.error('测试套件执行失败:', error)
    return {
      success: false,
      error: error
    }
  }
}

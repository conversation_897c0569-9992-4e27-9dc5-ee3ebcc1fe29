/**
 * Store 功能测试工具
 * 用于验证 Pinia stores 的基本功能
 */

import { useUserStore, useEmotionsStore } from '@/stores'

/**
 * 测试用户 Store 功能
 */
export const testUserStore = () => {
  console.log('=== 测试用户 Store ===')
  
  const userStore = useUserStore()
  
  // 测试初始状态
  console.log('初始登录状态:', userStore.isLoggedIn)
  console.log('用户名:', userStore.userName)
  
  // 测试模拟登录
  const mockUser = {
    user_id: 'test_user_001',
    open_id: 'test_openid',
    username: '测试用户',
    avatar_url: '',
    last_login_time: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }
  
  userStore.loginSuccess(mockUser)
  console.log('登录后状态:', userStore.isLoggedIn)
  console.log('登录后用户名:', userStore.userName)
  
  // 测试设置更新
  userStore.updateSettings({ theme: 'dark' })
  console.log('更新设置后主题:', userStore.appSettings.theme)
  
  return {
    success: true,
    message: '用户 Store 测试通过'
  }
}

/**
 * 测试情绪 Store 功能
 */
export const testEmotionsStore = () => {
  console.log('=== 测试情绪 Store ===')
  
  const emotionsStore = useEmotionsStore()
  
  // 测试初始数据
  console.log('情绪数量:', emotionsStore.emotions.length)
  console.log('原因数量:', emotionsStore.causes.length)
  console.log('记录数量:', emotionsStore.entries.length)
  
  // 测试情绪网格
  console.log('情绪网格:', emotionsStore.emotionGrid.length)
  
  // 测试原因分类
  console.log('原因分类:', emotionsStore.causeCategories)
  
  // 测试表单操作
  emotionsStore.updateCurrentForm({
    emotion_id: 1,
    cause_id: 1,
    intensity: 4,
    notes: '这是一条测试记录'
  })
  
  console.log('当前表单:', emotionsStore.currentForm)
  
  // 测试表单验证
  const validation = emotionsStore.validateForm(emotionsStore.currentForm)
  console.log('表单验证结果:', validation)
  
  // 测试添加记录
  if (validation.isValid) {
    const newEntry = emotionsStore.addEntry(emotionsStore.currentForm, 'test_user_001')
    console.log('新增记录:', newEntry)
    console.log('记录总数:', emotionsStore.entries.length)
    console.log('今日记录:', emotionsStore.todayEntries.length)
  }
  
  return {
    success: true,
    message: '情绪 Store 测试通过'
  }
}

/**
 * 测试数据持久化
 */
export const testDataPersistence = () => {
  console.log('=== 测试数据持久化 ===')
  
  try {
    // 测试存储数据
    const testData = { test: 'persistence_test', timestamp: Date.now() }
    uni.setStorageSync('test_key', JSON.stringify(testData))
    
    // 测试读取数据
    const retrievedData = uni.getStorageSync('test_key')
    const parsedData = JSON.parse(retrievedData)
    
    console.log('存储的数据:', testData)
    console.log('读取的数据:', parsedData)
    
    // 清理测试数据
    uni.removeStorageSync('test_key')
    
    const isMatch = parsedData.test === testData.test && parsedData.timestamp === testData.timestamp
    
    return {
      success: isMatch,
      message: isMatch ? '数据持久化测试通过' : '数据持久化测试失败'
    }
  } catch (error) {
    console.error('数据持久化测试失败:', error)
    return {
      success: false,
      message: '数据持久化测试失败: ' + error
    }
  }
}

/**
 * 运行所有测试
 */
export const runAllTests = () => {
  console.log('🧪 开始运行 Store 测试...')
  
  const results = [
    testDataPersistence(),
    testUserStore(),
    testEmotionsStore(),
  ]
  
  const allPassed = results.every(result => result.success)
  
  console.log('📊 测试结果汇总:')
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.message} ${result.success ? '✅' : '❌'}`)
  })
  
  if (allPassed) {
    console.log('🎉 所有测试通过！Store 功能正常')
    uni.showToast({
      title: '所有测试通过！',
      icon: 'success'
    })
  } else {
    console.log('⚠️ 部分测试失败，请检查相关功能')
    uni.showToast({
      title: '部分测试失败',
      icon: 'none'
    })
  }
  
  return {
    allPassed,
    results
  }
}

/**
 * 生成测试数据
 */
export const generateTestData = () => {
  console.log('=== 生成测试数据 ===')
  
  const emotionsStore = useEmotionsStore()
  const userStore = useUserStore()
  
  // 确保用户已登录
  if (!userStore.isLoggedIn) {
    const mockUser = {
      user_id: 'test_user_001',
      open_id: 'test_openid',
      username: '测试用户',
      avatar_url: '',
      last_login_time: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    userStore.loginSuccess(mockUser)
  }
  
  // 生成一周的测试数据
  const emotions = emotionsStore.emotions
  const causes = emotionsStore.causes
  
  for (let i = 0; i < 7; i++) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    
    // 每天生成 1-3 条记录
    const recordCount = Math.floor(Math.random() * 3) + 1
    
    for (let j = 0; j < recordCount; j++) {
      const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)]
      const randomCause = causes[Math.floor(Math.random() * causes.length)]
      
      const testForm = {
        emotion_id: randomEmotion.emotion_id,
        cause_id: randomCause.cause_id,
        intensity: Math.floor(Math.random() * 5) + 1,
        notes: `这是第${i + 1}天的第${j + 1}条测试记录。${randomEmotion.emotion_label}的感受来自${randomCause.cause_label}。`,
        date: new Date(date.getTime() + j * 60 * 60 * 1000).toISOString(), // 每条记录间隔1小时
      }
      
      emotionsStore.addEntry(testForm, userStore.userId)
    }
  }
  
  console.log(`生成了 ${emotionsStore.entries.length} 条测试记录`)
  
  uni.showToast({
    title: `生成了 ${emotionsStore.entries.length} 条测试数据`,
    icon: 'success'
  })
  
  return emotionsStore.entries.length
}

/**
 * 清除所有测试数据
 */
export const clearTestData = () => {
  console.log('=== 清除测试数据 ===')
  
  try {
    // 清除存储的数据
    uni.removeStorageSync('user_info')
    uni.removeStorageSync('login_state')
    uni.removeStorageSync('entries')
    uni.removeStorageSync('app_settings')
    
    // 重新初始化 stores
    const userStore = useUserStore()
    const emotionsStore = useEmotionsStore()
    
    userStore.init()
    emotionsStore.init()
    
    console.log('测试数据清除完成')
    
    uni.showToast({
      title: '测试数据已清除',
      icon: 'success'
    })
    
    return true
  } catch (error) {
    console.error('清除测试数据失败:', error)
    
    uni.showToast({
      title: '清除数据失败',
      icon: 'none'
    })
    
    return false
  }
}

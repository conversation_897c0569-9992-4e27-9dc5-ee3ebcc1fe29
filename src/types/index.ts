/**
 * 今日碎语小程序 - TypeScript 类型定义
 * 基于需求文档中的数据模型定义
 */

// ==================== 用户相关类型 ====================

/**
 * 用户信息接口
 */
export interface User {
  /** 内部用户唯一标识符 */
  user_id: string
  /** 微信OpenID */
  open_id: string
  /** 微信UnionID (可选) */
  union_id?: string
  /** 用户显示名称 */
  username: string
  /** 用户头像URL */
  avatar_url?: string
  /** 上次登录时间 */
  last_login_time: string
  /** 账户创建时间 */
  created_at: string
  /** 最后更新时间 */
  updated_at: string
}

/**
 * 用户登录状态
 */
export interface UserLoginState {
  /** 是否已登录 */
  isLoggedIn: boolean
  /** 登录中状态 */
  isLogging: boolean
  /** 微信授权码 */
  code?: string
  /** 会话密钥 (前端不存储，仅用于类型定义) */
  session_key?: string
}

// ==================== 情绪相关类型 ====================

/**
 * 情绪定义接口
 */
export interface Emotion {
  /** 情绪ID */
  emotion_id: number
  /** 情绪标签 */
  emotion_label: string
  /** Iconify 图标名称 */
  emotion_icon: string
  /** HEX颜色代码 */
  emotion_color: string
  /** 是否为系统定义 */
  is_system: boolean
  /** 关联用户ID */
  user_id: string
  /** 显示顺序 */
  sort_order?: number
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
}

/**
 * 原因定义接口
 */
export interface Cause {
  /** 原因ID */
  cause_id: number
  /** 原因标签 */
  cause_label: string
  /** 类别标签 */
  cause_category: string
  /** 是否为系统定义 */
  is_system: boolean
  /** 关联用户ID */
  user_id: string
  /** 显示顺序 */
  sort_order?: number
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
}

/**
 * 日记条目接口
 */
export interface Entry {
  /** 条目ID */
  entry_id: number
  /** 用户ID */
  user_id: string
  /** 记录时间戳 */
  date: string
  /** 日记内容 */
  notes: string
  /** 原因ID */
  cause_id: number
  /** 情绪ID */
  emotion_id: number
  /** 情绪强度 (1-5) */
  intensity: number
  /** 创建时间 */
  created_at: string
  /** 更新时间 */
  updated_at: string
  /** 关联的情绪对象 (计算属性) */
  emotion?: Emotion
  /** 关联的原因对象 (计算属性) */
  cause?: Cause
}

// ==================== API 相关类型 ====================

/**
 * API 响应基础接口
 */
export interface ApiResponse<T = any> {
  /** 状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 时间戳 */
  timestamp: string
  /** 请求ID (用于追踪) */
  request_id?: string
  /** API版本 */
  version?: string
}

/**
 * API 错误接口
 */
export interface ApiError {
  /** 错误码 */
  code: number
  /** 错误消息 */
  message: string
  /** 详细错误信息 */
  details?: string
  /** 错误字段 (表单验证错误) */
  field?: string
  /** 错误堆栈 (开发环境) */
  stack?: string
}

/**
 * 分页请求参数
 */
export interface PaginationParams {
  /** 页码 (从1开始) */
  page: number
  /** 每页数量 */
  page_size: number
  /** 排序字段 */
  sort_by?: string
  /** 排序方向 */
  sort_order?: 'asc' | 'desc'
}

/**
 * 分页响应数据
 */
export interface PaginationResponse<T> {
  /** 数据列表 */
  items: T[]
  /** 总数量 */
  total: number
  /** 当前页码 */
  page: number
  /** 每页数量 */
  page_size: number
  /** 总页数 */
  total_pages: number
  /** 是否有下一页 */
  has_next: boolean
  /** 是否有上一页 */
  has_prev: boolean
}

// ==================== API 请求/响应接口 ====================

/**
 * 微信登录请求 (匹配后端 WeChatLoginRequest)
 */
export interface WxLoginRequest {
  /** 微信授权码 */
  code: string
  /** 用户昵称 (可选) */
  nickname?: string
  /** 用户头像URL (可选) */
  avatarUrl?: string
}

/**
 * 微信登录响应
 */
export interface WxLoginResponse {
  /** 用户信息 */
  user: User
  /** 访问令牌 */
  access_token: string
  /** 刷新令牌 */
  refresh_token: string
  /** 令牌过期时间 (秒) */
  expires_in: number
}

/**
 * 刷新令牌请求
 */
export interface RefreshTokenRequest {
  /** 刷新令牌 */
  refresh_token: string
}

/**
 * 创建情绪记录请求
 */
export interface CreateEntryRequest {
  /** 情绪ID */
  emotion_id: number
  /** 原因ID */
  cause_id: number
  /** 情绪强度 */
  intensity: number
  /** 笔记内容 */
  notes: string
  /** 记录时间 (可选，默认当前时间) */
  date?: string
}

/**
 * 更新情绪记录请求
 */
export interface UpdateEntryRequest extends Partial<CreateEntryRequest> {
  /** 记录ID */
  entry_id: number
}

/**
 * 查询情绪记录请求
 */
export interface QueryEntriesRequest extends PaginationParams {
  /** 用户ID (可选，默认当前用户) */
  user_id?: string
  /** 情绪ID筛选 */
  emotion_id?: number
  /** 原因ID筛选 */
  cause_id?: number
  /** 开始日期 */
  start_date?: string
  /** 结束日期 */
  end_date?: string
  /** 最小强度 */
  min_intensity?: number
  /** 最大强度 */
  max_intensity?: number
  /** 关键词搜索 */
  keyword?: string
}

/**
 * 情绪统计请求
 */
export interface EmotionStatsRequest {
  /** 用户ID (可选) */
  user_id?: string
  /** 统计类型 */
  type: 'emotion' | 'cause' | 'intensity'
  /** 时间范围 */
  time_range: TimeRange
  /** 开始日期 */
  start_date?: string
  /** 结束日期 */
  end_date?: string
  /** 分组方式 */
  group_by?: 'day' | 'week' | 'month' | 'category'
}

/**
 * 情绪统计响应
 */
export interface EmotionStatsResponse {
  /** 统计数据 */
  stats: EmotionStats[] | CauseStats[]
  /** 时间轴数据 */
  timeline?: TimelinePoint[]
  /** 汇总信息 */
  summary: {
    total_entries: number
    avg_intensity: number
    most_common_emotion: string
    most_common_cause: string
    date_range: {
      start: string
      end: string
    }
  }
}

/**
 * 自定义情绪创建请求
 */
export interface CreateEmotionRequest {
  /** 情绪标签 */
  emotion_label: string
  /** 图标名称 */
  emotion_icon: string
  /** 颜色代码 */
  emotion_color: string
  /** 排序顺序 */
  sort_order?: number
}

/**
 * 自定义原因创建请求
 */
export interface CreateCauseRequest {
  /** 原因标签 */
  cause_label: string
  /** 类别标签 */
  cause_category: string
  /** 排序顺序 */
  sort_order?: number
}

/**
 * 数据导出请求
 */
export interface DataExportRequest {
  /** 导出格式 */
  format: 'json' | 'csv' | 'excel'
  /** 数据类型 */
  data_types: ('entries' | 'emotions' | 'causes')[]
  /** 日期范围 */
  date_range?: {
    start: string
    end: string
  }
  /** 是否包含统计信息 */
  include_stats?: boolean
}

/**
 * 数据导出响应
 */
export interface DataExportResponse {
  /** 下载链接 */
  download_url: string
  /** 文件名 */
  filename: string
  /** 文件大小 (字节) */
  file_size: number
  /** 过期时间 */
  expires_at: string
}

// ==================== 表单相关类型 ====================

/**
 * 情绪记录表单数据
 */
export interface EmotionRecordForm {
  /** 选中的情绪ID */
  emotion_id?: number
  /** 选中的原因ID */
  cause_id?: number
  /** 情绪强度 */
  intensity: number
  /** 笔记内容 */
  notes: string
  /** 记录时间 */
  date?: string
}

/**
 * 表单验证结果
 */
export interface FormValidationResult {
  /** 是否有效 */
  isValid: boolean
  /** 错误信息 */
  errors: Record<string, string>
}

// ==================== 图表分析相关类型 ====================

/**
 * 时间范围类型
 */
export type TimeRange = 'day' | 'week' | 'month'

/**
 * 图表类型
 */
export type ChartType = 'emotion' | 'cause'

/**
 * 情绪统计数据
 */
export interface EmotionStats {
  /** 情绪信息 */
  emotion: Emotion
  /** 出现次数 */
  count: number
  /** 占比百分比 */
  percentage: number
  /** 平均强度 */
  avg_intensity: number
}

/**
 * 原因统计数据
 */
export interface CauseStats {
  /** 原因信息 */
  cause: Cause
  /** 出现次数 */
  count: number
  /** 占比百分比 */
  percentage: number
}

/**
 * 时间轴数据点
 */
export interface TimelinePoint {
  /** 时间戳 */
  timestamp: string
  /** 条目信息 */
  entry: Entry
  /** 显示位置 (用于时间轴布局) */
  position?: number
}

// ==================== 存储相关类型 ====================

/**
 * 本地存储键名常量
 */
export const STORAGE_KEYS = {
  /** 用户信息 */
  USER_INFO: 'user_info',
  /** 登录状态 */
  LOGIN_STATE: 'login_state',
  /** 情绪数据 */
  EMOTIONS: 'emotions',
  /** 原因数据 */
  CAUSES: 'causes',
  /** 日记条目 */
  ENTRIES: 'entries',
  /** 应用设置 */
  APP_SETTINGS: 'app_settings',
} as const

/**
 * 应用设置接口
 */
export interface AppSettings {
  /** 主题模式 */
  theme: 'light' | 'dark' | 'auto'
  /** 语言设置 */
  language: 'zh-CN' | 'en-US'
  /** 是否启用通知 */
  notifications_enabled: boolean
  /** 数据同步设置 */
  auto_sync: boolean
  /** 数据缓存时间 (分钟) */
  cache_duration: number
  /** 离线模式 */
  offline_mode: boolean
}

// ==================== 数据验证相关类型 ====================

/**
 * 验证规则接口
 */
export interface ValidationRule {
  /** 规则类型 */
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  /** 规则值 */
  value?: any
  /** 错误消息 */
  message: string
  /** 自定义验证函数 */
  validator?: (value: any) => boolean
}

/**
 * 字段验证配置
 */
export interface FieldValidation {
  /** 字段名 */
  field: string
  /** 验证规则 */
  rules: ValidationRule[]
  /** 是否必填 */
  required?: boolean
}

/**
 * 验证结果
 */
export interface ValidationResult {
  /** 是否通过验证 */
  valid: boolean
  /** 错误信息 */
  errors: Record<string, string[]>
  /** 警告信息 */
  warnings?: Record<string, string[]>
}

/**
 * 数据验证模式
 */
export interface ValidationSchema {
  /** 模式名称 */
  name: string
  /** 字段验证配置 */
  fields: FieldValidation[]
  /** 自定义验证函数 */
  customValidators?: Record<string, (data: any) => ValidationResult>
}

// ==================== 数据转换相关类型 ====================

/**
 * 数据转换器接口
 */
export interface DataTransformer<TInput, TOutput> {
  /** 转换函数 */
  transform: (input: TInput) => TOutput
  /** 反向转换函数 */
  reverse?: (output: TOutput) => TInput
  /** 验证输入数据 */
  validate?: (input: TInput) => boolean
}

/**
 * 前端到后端数据转换
 */
export interface FrontendToBackendTransform {
  /** 用户数据转换 */
  user: DataTransformer<User, any>
  /** 情绪记录转换 */
  entry: DataTransformer<EmotionRecordForm, CreateEntryRequest>
  /** 情绪定义转换 */
  emotion: DataTransformer<Emotion, CreateEmotionRequest>
  /** 原因定义转换 */
  cause: DataTransformer<Cause, CreateCauseRequest>
}

/**
 * 后端到前端数据转换
 */
export interface BackendToFrontendTransform {
  /** 用户数据转换 */
  user: DataTransformer<any, User>
  /** 情绪记录转换 */
  entry: DataTransformer<any, Entry>
  /** 情绪定义转换 */
  emotion: DataTransformer<any, Emotion>
  /** 原因定义转换 */
  cause: DataTransformer<any, Cause>
}

// ==================== 缓存和同步相关类型 ====================

/**
 * 缓存项接口
 */
export interface CacheItem<T> {
  /** 缓存键 */
  key: string
  /** 缓存数据 */
  data: T
  /** 创建时间 */
  created_at: number
  /** 过期时间 */
  expires_at: number
  /** 版本号 */
  version: number
}

/**
 * 缓存配置
 */
export interface CacheConfig {
  /** 默认过期时间 (毫秒) */
  default_ttl: number
  /** 最大缓存大小 */
  max_size: number
  /** 清理策略 */
  cleanup_strategy: 'lru' | 'fifo' | 'ttl'
  /** 是否启用压缩 */
  compression: boolean
}

/**
 * 数据同步状态
 */
export interface SyncStatus {
  /** 同步状态 */
  status: 'idle' | 'syncing' | 'success' | 'error'
  /** 最后同步时间 */
  last_sync: string
  /** 待同步项目数 */
  pending_count: number
  /** 错误信息 */
  error_message?: string
}

/**
 * 同步队列项
 */
export interface SyncQueueItem {
  /** 唯一ID */
  id: string
  /** 操作类型 */
  action: 'create' | 'update' | 'delete'
  /** 数据类型 */
  type: 'user' | 'entry' | 'emotion' | 'cause'
  /** 数据内容 */
  data: any
  /** 创建时间 */
  created_at: string
  /** 重试次数 */
  retry_count: number
  /** 最大重试次数 */
  max_retries: number
}

// ==================== 错误处理相关类型 ====================

/**
 * 错误类型枚举
 */
export enum ErrorType {
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** 认证错误 */
  AUTH_ERROR = 'AUTH_ERROR',
  /** 验证错误 */
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  /** 服务器错误 */
  SERVER_ERROR = 'SERVER_ERROR',
  /** 客户端错误 */
  CLIENT_ERROR = 'CLIENT_ERROR',
  /** 未知错误 */
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 应用错误接口
 */
export interface AppError extends Error {
  /** 错误类型 */
  type: ErrorType
  /** 错误码 */
  code: string
  /** 详细信息 */
  details?: any
  /** 是否可重试 */
  retryable: boolean
  /** 时间戳 */
  timestamp: string
}

/**
 * 错误处理器配置
 */
export interface ErrorHandlerConfig {
  /** 是否显示错误提示 */
  show_toast: boolean
  /** 是否记录错误日志 */
  log_error: boolean
  /** 是否上报错误 */
  report_error: boolean
  /** 重试配置 */
  retry_config?: {
    max_retries: number
    retry_delay: number
    backoff_factor: number
  }
}

// ==================== 环境配置相关类型 ====================

/**
 * 环境类型
 */
export type Environment = 'development' | 'staging' | 'production'

/**
 * API 环境配置
 */
export interface ApiConfig {
  /** 基础URL */
  base_url: string
  /** 超时时间 (毫秒) */
  timeout: number
  /** 重试次数 */
  retry_count: number
  /** 是否启用日志 */
  enable_logging: boolean
  /** 是否启用模拟数据 */
  enable_mock: boolean
}

/**
 * 应用配置
 */
export interface AppConfig {
  /** 环境 */
  environment: Environment
  /** API配置 */
  api: ApiConfig
  /** 缓存配置 */
  cache: CacheConfig
  /** 错误处理配置 */
  error_handler: ErrorHandlerConfig
  /** 功能开关 */
  features: {
    enable_analytics: boolean
    enable_crash_reporting: boolean
    enable_performance_monitoring: boolean
    enable_offline_mode: boolean
  }
}

/**
 * 情绪记录状态管理 Store
 * 管理日记条目、情绪定义和原因分类数据
 */
import type {
  Cause,
  Emotion,
  EmotionRecordForm,
  Entry,
} from '@/types'

import { defineStore } from 'pinia'

import { computed, ref } from 'vue'

import { STORAGE_KEYS } from '@/types'

export const useEmotionsStore = defineStore('emotions', () => {
  // ==================== 状态定义 ====================

  /** 日记条目列表 */
  const entries = ref<Entry[]>([])

  /** 情绪定义列表 */
  const emotions = ref<Emotion[]>([])

  /** 原因定义列表 */
  const causes = ref<Cause[]>([])

  /** 当前正在编辑的表单数据 */
  const currentForm = ref<EmotionRecordForm>({
    intensity: 3,
    notes: '',
  })

  /** 加载状态 */
  const loading = ref(false)

  // ==================== 计算属性 ====================

  /** 今日条目 */
  const todayEntries = computed(() => {
    const today = new Date().toDateString()
    return entries.value.filter(entry =>
      new Date(entry.date).toDateString() === today,
    )
  })

  /** 最新条目 */
  const latestEntry = computed(() => {
    if (entries.value.length === 0) { return null }
    return entries.value.reduce((latest, current) =>
      new Date(current.date) > new Date(latest.date) ? current : latest,
    )
  })

  /** 今日最新条目 */
  const todayLatestEntry = computed(() => {
    if (todayEntries.value.length === 0) { return null }
    return todayEntries.value.reduce((latest, current) =>
      new Date(current.date) > new Date(latest.date) ? current : latest,
    )
  })

  /** 情绪分类 (按网格排列) */
  const emotionGrid = computed(() => {
    const sortedEmotions = [...emotions.value].sort((a, b) =>
      (a.sort_order || 0) - (b.sort_order || 0),
    )

    // 确保有9个情绪用于3x3网格
    while (sortedEmotions.length < 9) {
      sortedEmotions.push({
        emotion_id: -sortedEmotions.length,
        emotion_label: '其他',
        emotion_icon: 'mdi:emoticon-neutral-outline',
        emotion_color: '#8ECAE6',
        is_system: true,
        user_id: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
    }

    return sortedEmotions.slice(0, 9)
  })

  /** 原因分类 */
  const causeCategories = computed(() => {
    const categories = [...new Set(causes.value.map(cause => cause.cause_category))]
    return categories.sort()
  })

  /** 根据分类获取原因列表 */
  const getCausesByCategory = computed(() => (category: string) => {
    return causes.value
      .filter(cause => cause.cause_category === category)
      .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
  })

  // ==================== 存储操作 ====================

  /**
   * 从本地存储加载数据
   */
  const loadFromStorage = () => {
    try {
      // 加载条目数据
      const storedEntries = uni.getStorageSync(STORAGE_KEYS.ENTRIES)
      if (storedEntries) {
        entries.value = JSON.parse(storedEntries)
      }

      // 加载情绪数据
      const storedEmotions = uni.getStorageSync(STORAGE_KEYS.EMOTIONS)
      if (storedEmotions) {
        emotions.value = JSON.parse(storedEmotions)
      }
      else {
        // 初始化默认情绪数据
        initDefaultEmotions()
      }

      // 加载原因数据
      const storedCauses = uni.getStorageSync(STORAGE_KEYS.CAUSES)
      if (storedCauses) {
        causes.value = JSON.parse(storedCauses)
      }
      else {
        // 初始化默认原因数据
        initDefaultCauses()
      }

      console.log('情绪数据加载成功')
    }
    catch (error) {
      console.error('加载情绪数据失败:', error)
    }
  }

  /**
   * 保存条目数据到本地存储
   */
  const saveEntries = () => {
    try {
      uni.setStorageSync(STORAGE_KEYS.ENTRIES, JSON.stringify(entries.value))
    }
    catch (error) {
      console.error('保存条目数据失败:', error)
    }
  }

  /**
   * 保存情绪数据到本地存储
   */
  const saveEmotions = () => {
    try {
      uni.setStorageSync(STORAGE_KEYS.EMOTIONS, JSON.stringify(emotions.value))
    }
    catch (error) {
      console.error('保存情绪数据失败:', error)
    }
  }

  /**
   * 保存原因数据到本地存储
   */
  const saveCauses = () => {
    try {
      uni.setStorageSync(STORAGE_KEYS.CAUSES, JSON.stringify(causes.value))
    }
    catch (error) {
      console.error('保存原因数据失败:', error)
    }
  }

  // ==================== 数据初始化 ====================

  /**
   * 初始化默认情绪数据
   */
  const initDefaultEmotions = () => {
    const defaultEmotions: Emotion[] = [
      {
        emotion_id: 1,
        emotion_label: '开心',
        emotion_icon: 'mdi:emoticon-happy-outline',
        emotion_color: '#FFD93D',
        is_system: true,
        user_id: 'system',
        sort_order: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        emotion_id: 2,
        emotion_label: '平静',
        emotion_icon: 'mdi:emoticon-neutral-outline',
        emotion_color: '#8ECAE6',
        is_system: true,
        user_id: 'system',
        sort_order: 2,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        emotion_id: 3,
        emotion_label: '难过',
        emotion_icon: 'mdi:emoticon-sad-outline',
        emotion_color: '#6C9BD1',
        is_system: true,
        user_id: 'system',
        sort_order: 3,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        emotion_id: 4,
        emotion_label: '焦虑',
        emotion_icon: 'mdi:emoticon-confused-outline',
        emotion_color: '#F4A261',
        is_system: true,
        user_id: 'system',
        sort_order: 4,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        emotion_id: 5,
        emotion_label: '愤怒',
        emotion_icon: 'mdi:emoticon-angry-outline',
        emotion_color: '#E76F51',
        is_system: true,
        user_id: 'system',
        sort_order: 5,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        emotion_id: 6,
        emotion_label: '兴奋',
        emotion_icon: 'mdi:emoticon-excited-outline',
        emotion_color: '#F72585',
        is_system: true,
        user_id: 'system',
        sort_order: 6,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        emotion_id: 7,
        emotion_label: '疲惫',
        emotion_icon: 'mdi:emoticon-dead-outline',
        emotion_color: '#A8DADC',
        is_system: true,
        user_id: 'system',
        sort_order: 7,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        emotion_id: 8,
        emotion_label: '感激',
        emotion_icon: 'mdi:heart-outline',
        emotion_color: '#F1FAEE',
        is_system: true,
        user_id: 'system',
        sort_order: 8,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        emotion_id: 9,
        emotion_label: '困惑',
        emotion_icon: 'mdi:help-circle-outline',
        emotion_color: '#457B9D',
        is_system: true,
        user_id: 'system',
        sort_order: 9,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ]

    emotions.value = defaultEmotions
    saveEmotions()
  }

  /**
   * 初始化默认原因数据
   */
  const initDefaultCauses = () => {
    const defaultCauses: Cause[] = [
      // 工作相关
      { cause_id: 1, cause_label: '工作压力', cause_category: '工作', is_system: true, user_id: 'system', sort_order: 1, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { cause_id: 2, cause_label: '项目进展', cause_category: '工作', is_system: true, user_id: 'system', sort_order: 2, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { cause_id: 3, cause_label: '同事关系', cause_category: '工作', is_system: true, user_id: 'system', sort_order: 3, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },

      // 社交相关
      { cause_id: 4, cause_label: '朋友聚会', cause_category: '社交', is_system: true, user_id: 'system', sort_order: 1, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { cause_id: 5, cause_label: '家庭关系', cause_category: '社交', is_system: true, user_id: 'system', sort_order: 2, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { cause_id: 6, cause_label: '恋爱关系', cause_category: '社交', is_system: true, user_id: 'system', sort_order: 3, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },

      // 健康相关
      { cause_id: 7, cause_label: '身体状况', cause_category: '健康', is_system: true, user_id: 'system', sort_order: 1, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { cause_id: 8, cause_label: '睡眠质量', cause_category: '健康', is_system: true, user_id: 'system', sort_order: 2, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { cause_id: 9, cause_label: '运动锻炼', cause_category: '健康', is_system: true, user_id: 'system', sort_order: 3, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },

      // 生活相关
      { cause_id: 10, cause_label: '日常琐事', cause_category: '生活', is_system: true, user_id: 'system', sort_order: 1, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { cause_id: 11, cause_label: '财务状况', cause_category: '生活', is_system: true, user_id: 'system', sort_order: 2, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { cause_id: 12, cause_label: '学习成长', cause_category: '生活', is_system: true, user_id: 'system', sort_order: 3, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },

      // 其他
      { cause_id: 13, cause_label: '天气变化', cause_category: '其他', is_system: true, user_id: 'system', sort_order: 1, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { cause_id: 14, cause_label: '突发事件', cause_category: '其他', is_system: true, user_id: 'system', sort_order: 2, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { cause_id: 15, cause_label: '无特定原因', cause_category: '其他', is_system: true, user_id: 'system', sort_order: 3, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    ]

    causes.value = defaultCauses
    saveCauses()
  }

  // ==================== 条目操作 ====================

  /**
   * 添加新的日记条目
   */
  const addEntry = (form: EmotionRecordForm, userId: string): Entry => {
    const newEntry: Entry = {
      entry_id: Date.now(), // 简单的ID生成，实际应该由后端生成
      user_id: userId,
      date: form.date || new Date().toISOString(),
      notes: form.notes,
      cause_id: form.cause_id!,
      emotion_id: form.emotion_id!,
      intensity: form.intensity,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    // 添加关联的情绪和原因对象
    newEntry.emotion = emotions.value.find(e => e.emotion_id === newEntry.emotion_id)
    newEntry.cause = causes.value.find(c => c.cause_id === newEntry.cause_id)

    entries.value.unshift(newEntry) // 添加到开头
    saveEntries()

    console.log('新增日记条目:', newEntry)
    return newEntry
  }

  /**
   * 更新日记条目
   */
  const updateEntry = (entryId: number, updates: Partial<Entry>) => {
    const index = entries.value.findIndex(entry => entry.entry_id === entryId)
    if (index !== -1) {
      entries.value[index] = {
        ...entries.value[index],
        ...updates,
        updated_at: new Date().toISOString(),
      }

      // 更新关联对象
      if (updates.emotion_id) {
        entries.value[index].emotion = emotions.value.find(e => e.emotion_id === updates.emotion_id)
      }
      if (updates.cause_id) {
        entries.value[index].cause = causes.value.find(c => c.cause_id === updates.cause_id)
      }

      saveEntries()
      console.log('更新日记条目:', entries.value[index])
    }
  }

  /**
   * 删除日记条目
   */
  const deleteEntry = (entryId: number) => {
    const index = entries.value.findIndex(entry => entry.entry_id === entryId)
    if (index !== -1) {
      const deletedEntry = entries.value.splice(index, 1)[0]
      saveEntries()
      console.log('删除日记条目:', deletedEntry)
      return deletedEntry
    }
    return null
  }

  // ==================== 表单操作 ====================

  /**
   * 更新当前表单数据
   */
  const updateCurrentForm = (updates: Partial<EmotionRecordForm>) => {
    currentForm.value = { ...currentForm.value, ...updates }
  }

  /**
   * 重置当前表单
   */
  const resetCurrentForm = () => {
    currentForm.value = {
      intensity: 3,
      notes: '',
    }
  }

  /**
   * 验证表单数据
   */
  const validateForm = (form: EmotionRecordForm) => {
    const errors: Record<string, string> = {}

    if (!form.emotion_id) {
      errors.emotion_id = '请选择情绪'
    }

    if (!form.cause_id) {
      errors.cause_id = '请选择原因'
    }

    if (form.intensity < 1 || form.intensity > 5) {
      errors.intensity = '情绪强度必须在1-5之间'
    }

    if (form.notes.length > 500) {
      errors.notes = '笔记内容不能超过500字'
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    }
  }

  // ==================== 初始化 ====================

  /**
   * 初始化情绪 Store
   */
  const init = () => {
    loadFromStorage()
  }

  // 返回 store 接口
  return {
    // 状态
    entries,
    emotions,
    causes,
    currentForm,
    loading,

    // 计算属性
    todayEntries,
    latestEntry,
    todayLatestEntry,
    emotionGrid,
    causeCategories,
    getCausesByCategory,

    // 方法
    addEntry,
    updateEntry,
    deleteEntry,
    updateCurrentForm,
    resetCurrentForm,
    validateForm,
    init,
  }
})

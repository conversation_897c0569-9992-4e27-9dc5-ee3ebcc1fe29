/**
 * 增强版情绪记录状态管理 Store
 * 集成新的数据层服务和高级状态管理模式
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  Entry, 
  Emotion, 
  Cause, 
  EmotionRecordForm, 
  EmotionStats, 
  CauseStats,
  TimeRange,
  QueryEntriesRequest,
  CreateEntryRequest,
  PaginationResponse,
  AppError
} from '@/types'
import { STORAGE_KEYS, ErrorType } from '@/types'
import { entryApi, emotionApi, causeApi, statsApi } from '@/services/api'
import { 
  useAsyncState, 
  useOptimisticState, 
  useCacheManager, 
  useSyncManager,
  withRetry,
  useDebounce
} from './composables'

export const useEnhancedEmotionsStore = defineStore('enhanced-emotions', () => {
  // ==================== 基础状态 ====================
  
  /** 日记条目列表 */
  const entries = ref<Entry[]>([])
  
  /** 情绪定义列表 */
  const emotions = ref<Emotion[]>([])
  
  /** 原因定义列表 */
  const causes = ref<Cause[]>([])
  
  /** 当前正在编辑的表单数据 */
  const currentForm = ref<EmotionRecordForm>({
    intensity: 3,
    notes: '',
  })
  
  /** 加载状态 */
  const loading = ref(false)

  // ==================== 高级状态管理 ====================

  /** 缓存管理器 */
  const cacheManager = useCacheManager<any>({
    default_ttl: 15 * 60 * 1000, // 15分钟
    max_size: 200,
    cleanup_strategy: 'lru',
    compression: false
  })

  /** 情绪列表异步状态 */
  const emotionsState = useAsyncState(
    async () => {
      const cacheKey = 'emotions_list'
      const cached = cacheManager.get(cacheKey)
      if (cached) return cached

      const response = await emotionApi.getAll()
      const emotionsList = response.data
      
      cacheManager.set(cacheKey, emotionsList)
      return emotionsList
    },
    [],
    {
      immediate: true,
      onSuccess: (data) => {
        emotions.value = data
      }
    }
  )

  /** 原因列表异步状态 */
  const causesState = useAsyncState(
    async () => {
      const cacheKey = 'causes_list'
      const cached = cacheManager.get(cacheKey)
      if (cached) return cached

      const response = await causeApi.getAll()
      const causesList = response.data
      
      cacheManager.set(cacheKey, causesList)
      return causesList
    },
    [],
    {
      immediate: true,
      onSuccess: (data) => {
        causes.value = data
      }
    }
  )

  /** 条目列表乐观更新状态 */
  const optimisticEntries = useOptimisticState<Entry[]>([])

  /** 同步管理器 */
  const syncManager = useSyncManager(async (item) => {
    switch (item.type) {
      case 'entry':
        switch (item.action) {
          case 'create':
            await entryApi.create(item.data)
            break
          case 'update':
            await entryApi.update(item.data.entry_id, item.data)
            break
          case 'delete':
            await entryApi.delete(item.data.entry_id)
            break
        }
        break
      default:
        throw new Error(`未知的同步类型: ${item.type}`)
    }
  })

  // ==================== 计算属性 ====================
  
  /** 今日条目 */
  const todayEntries = computed(() => {
    const today = new Date().toDateString()
    return entries.value.filter(entry => 
      new Date(entry.date).toDateString() === today
    )
  })
  
  /** 最新条目 */
  const latestEntry = computed(() => {
    if (entries.value.length === 0) return null
    return entries.value.reduce((latest, current) => 
      new Date(current.date) > new Date(latest.date) ? current : latest
    )
  })
  
  /** 今日最新条目 */
  const todayLatestEntry = computed(() => {
    if (todayEntries.value.length === 0) return null
    return todayEntries.value.reduce((latest, current) => 
      new Date(current.date) > new Date(latest.date) ? current : latest
    )
  })
  
  /** 情绪分类 (按网格排列) */
  const emotionGrid = computed(() => {
    const sortedEmotions = [...emotions.value].sort((a, b) => 
      (a.sort_order || 0) - (b.sort_order || 0)
    )
    
    // 确保有9个情绪用于3x3网格
    while (sortedEmotions.length < 9 && sortedEmotions.length > 0) {
      const lastEmotion = sortedEmotions[sortedEmotions.length - 1]
      sortedEmotions.push({
        ...lastEmotion,
        emotion_id: -sortedEmotions.length,
        emotion_label: '其他',
        emotion_icon: 'mdi:emoticon-neutral-outline',
        emotion_color: '#8ECAE6',
      })
    }
    
    return sortedEmotions.slice(0, 9)
  })
  
  /** 原因分类 */
  const causeCategories = computed(() => {
    const categories = [...new Set(causes.value.map(cause => cause.cause_category))]
    return categories.sort()
  })
  
  /** 根据分类获取原因列表 */
  const getCausesByCategory = computed(() => (category: string) => {
    return causes.value
      .filter(cause => cause.cause_category === category)
      .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
  })

  /** 是否有待同步数据 */
  const hasPendingSync = computed(() => syncManager.queue.value.length > 0)

  /** 同步状态 */
  const syncStatus = computed(() => syncManager.status.value)

  // ==================== 数据加载 ====================

  /**
   * 加载条目列表 (带缓存)
   */
  const loadEntries = useAsyncState(
    async (params: QueryEntriesRequest = { page: 1, page_size: 50 }) => {
      const cacheKey = `entries_${JSON.stringify(params)}`
      const cached = cacheManager.get(cacheKey)
      if (cached) return cached

      const response = await entryApi.query(params)
      const result = response.data
      
      // 缓存结果
      cacheManager.set(cacheKey, result, 5 * 60 * 1000) // 5分钟缓存
      
      return result
    },
    null,
    {
      immediate: false,
      onSuccess: (data) => {
        if (data) {
          entries.value = data.items
          optimisticEntries.data.value = data.items
        }
      }
    }
  )

  /**
   * 加载统计数据
   */
  const loadStats = useAsyncState(
    async (params: { type: 'emotion' | 'cause'; time_range: TimeRange }) => {
      const cacheKey = `stats_${params.type}_${params.time_range}`
      const cached = cacheManager.get(cacheKey)
      if (cached) return cached

      const response = params.type === 'emotion' 
        ? await statsApi.getEmotionStats({ ...params, type: 'emotion' })
        : await statsApi.getCauseStats({ ...params, type: 'cause' })
      
      const result = response.data
      
      // 缓存统计数据
      cacheManager.set(cacheKey, result, 10 * 60 * 1000) // 10分钟缓存
      
      return result
    },
    null,
    { immediate: false }
  )

  // ==================== 条目操作 ====================
  
  /**
   * 添加新的日记条目 (带乐观更新)
   */
  const addEntry = async (form: EmotionRecordForm, userId: string): Promise<Entry> => {
    const validation = validateForm(form)
    if (!validation.isValid) {
      const firstError = Object.values(validation.errors)[0]
      throw new Error(firstError)
    }

    const entryData: CreateEntryRequest = {
      emotion_id: form.emotion_id!,
      cause_id: form.cause_id!,
      intensity: form.intensity,
      notes: form.notes,
      date: form.date || new Date().toISOString()
    }

    try {
      return await optimisticEntries.optimisticUpdate(
        (currentEntries) => {
          // 创建临时条目用于乐观更新
          const tempEntry: Entry = {
            entry_id: Date.now(), // 临时ID
            user_id: userId,
            date: entryData.date!,
            notes: entryData.notes,
            cause_id: entryData.cause_id,
            emotion_id: entryData.emotion_id,
            intensity: entryData.intensity,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            emotion: emotions.value.find(e => e.emotion_id === entryData.emotion_id),
            cause: causes.value.find(c => c.cause_id === entryData.cause_id)
          }
          
          return [tempEntry, ...currentEntries]
        },
        async () => {
          const response = await entryApi.create(entryData)
          const newEntry = response.data
          
          // 添加关联对象
          newEntry.emotion = emotions.value.find(e => e.emotion_id === newEntry.emotion_id)
          newEntry.cause = causes.value.find(c => c.cause_id === newEntry.cause_id)
          
          // 更新本地状态
          entries.value = [newEntry, ...entries.value.filter(e => e.entry_id !== Date.now())]
          
          // 清除相关缓存
          cacheManager.clear()
          
          return newEntry
        }
      )
    } catch (error) {
      // 如果在线创建失败，添加到同步队列
      syncManager.enqueue({
        action: 'create',
        type: 'entry',
        data: entryData,
        max_retries: 3
      })
      throw error
    }
  }
  
  /**
   * 更新日记条目 (带重试)
   */
  const updateEntry = async (entryId: number, updates: Partial<Entry>) => {
    try {
      const response = await withRetry(
        () => entryApi.update(entryId, { entry_id: entryId, ...updates }),
        {
          maxRetries: 3,
          delay: 1000,
          backoffFactor: 2,
          shouldRetry: (error) => error.type === ErrorType.NETWORK_ERROR
        }
      )

      const updatedEntry = response.data
      
      // 更新本地状态
      const index = entries.value.findIndex(entry => entry.entry_id === entryId)
      if (index !== -1) {
        // 添加关联对象
        updatedEntry.emotion = emotions.value.find(e => e.emotion_id === updatedEntry.emotion_id)
        updatedEntry.cause = causes.value.find(c => c.cause_id === updatedEntry.cause_id)
        
        entries.value[index] = updatedEntry
        optimisticEntries.data.value = [...entries.value]
      }
      
      // 清除相关缓存
      cacheManager.clear()
      
      return updatedEntry
    } catch (error) {
      // 如果在线更新失败，添加到同步队列
      syncManager.enqueue({
        action: 'update',
        type: 'entry',
        data: { entry_id: entryId, ...updates },
        max_retries: 3
      })
      throw error
    }
  }
  
  /**
   * 删除日记条目
   */
  const deleteEntry = async (entryId: number) => {
    try {
      await entryApi.delete(entryId)
      
      // 更新本地状态
      const index = entries.value.findIndex(entry => entry.entry_id === entryId)
      if (index !== -1) {
        const deletedEntry = entries.value.splice(index, 1)[0]
        optimisticEntries.data.value = [...entries.value]
        
        // 清除相关缓存
        cacheManager.clear()
        
        return deletedEntry
      }
    } catch (error) {
      // 如果在线删除失败，添加到同步队列
      syncManager.enqueue({
        action: 'delete',
        type: 'entry',
        data: { entry_id: entryId },
        max_retries: 3
      })
      throw error
    }
    
    return null
  }

  // ==================== 表单操作 ====================
  
  /**
   * 更新当前表单数据 (防抖)
   */
  const updateCurrentForm = useDebounce((updates: Partial<EmotionRecordForm>) => {
    currentForm.value = { ...currentForm.value, ...updates }
  }, 100)
  
  /**
   * 重置当前表单
   */
  const resetCurrentForm = () => {
    currentForm.value = {
      intensity: 3,
      notes: '',
    }
  }
  
  /**
   * 验证表单数据
   */
  const validateForm = (form: EmotionRecordForm) => {
    const errors: Record<string, string> = {}
    
    if (!form.emotion_id) {
      errors.emotion_id = '请选择情绪'
    }
    
    if (!form.cause_id) {
      errors.cause_id = '请选择原因'
    }
    
    if (form.intensity < 1 || form.intensity > 5) {
      errors.intensity = '情绪强度必须在1-5之间'
    }
    
    if (form.notes.length > 500) {
      errors.notes = '笔记内容不能超过500字'
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    }
  }

  // ==================== 数据刷新 ====================

  /**
   * 刷新所有数据
   */
  const refreshAll = async () => {
    // 清除缓存
    cacheManager.clear()
    
    // 重新加载数据
    await Promise.all([
      emotionsState.reload(),
      causesState.reload(),
      loadEntries.reload()
    ])
  }

  /**
   * 刷新条目列表
   */
  const refreshEntries = async (params?: QueryEntriesRequest) => {
    cacheManager.clear()
    await loadEntries.reload()
  }

  // ==================== 同步操作 ====================

  /**
   * 手动同步数据
   */
  const syncData = async () => {
    await syncManager.sync()
  }

  /**
   * 重试失败的同步
   */
  const retrySync = async () => {
    await syncManager.retryFailed()
  }

  // ==================== 初始化 ====================
  
  /**
   * 初始化情绪 Store
   */
  const init = () => {
    // 异步状态会自动加载情绪和原因数据
    // 手动加载条目数据
    loadEntries.reload()
  }

  // 返回 store 接口
  return {
    // 基础状态
    entries,
    emotions,
    causes,
    currentForm,
    loading,
    
    // 异步状态
    emotionsState,
    causesState,
    loadEntries,
    loadStats,
    optimisticEntries,
    syncStatus,
    hasPendingSync,
    
    // 计算属性
    todayEntries,
    latestEntry,
    todayLatestEntry,
    emotionGrid,
    causeCategories,
    getCausesByCategory,
    
    // 方法
    addEntry,
    updateEntry,
    deleteEntry,
    updateCurrentForm,
    resetCurrentForm,
    validateForm,
    refreshAll,
    refreshEntries,
    syncData,
    retrySync,
    init,
  }
})

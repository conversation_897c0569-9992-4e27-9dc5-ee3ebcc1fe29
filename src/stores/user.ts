/**
 * 用户状态管理 Store
 * 集成新的数据层服务和高级状态管理模式
 */

import type { AppError, AppSettings, User, UserLoginState, WxLoginRequest } from '@/types'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { apiClient, authApi, userApi } from '@/services/api'
import { ErrorType, STORAGE_KEYS } from '@/types'
import {
  useAsyncState,
  useCacheManager,
  useDebounce,
  useOptimisticState,
  useSyncManager,
  withRetry,
} from './composables'

export const useUserStore = defineStore('user', () => {
  // ==================== 基础状态 ====================

  /** 用户信息 */
  const userInfo = ref<User | null>(null)

  /** 登录状态 */
  const loginState = ref<UserLoginState>({
    isLoggedIn: false,
    isLogging: false,
  })

  /** 应用设置 */
  const appSettings = ref<AppSettings>({
    theme: 'light',
    language: 'zh-CN',
    notifications_enabled: true,
    auto_sync: true,
    cache_duration: 30,
    offline_mode: false,
  })

  // ==================== 高级状态管理 ====================

  /** 缓存管理器 */
  const cacheManager = useCacheManager<any>({
    default_ttl: 30 * 60 * 1000, // 30分钟
    max_size: 100,
    cleanup_strategy: 'lru',
    compression: false,
  })

  /** 用户资料异步状态 */
  const profileState = useAsyncState(
    async () => {
      if (!userInfo.value?.user_id) {
        throw new Error('用户未登录')
      }

      // 先尝试从缓存获取
      const cacheKey = `user_profile_${userInfo.value.user_id}`
      const cached = cacheManager.get(cacheKey)
      if (cached) {
        return cached
      }

      // 从 API 获取
      const response = await userApi.getProfile()
      const profile = response.data

      // 缓存结果
      cacheManager.set(cacheKey, profile)

      return profile
    },
    null,
    {
      immediate: false,
      onError: (error) => {
        console.error('获取用户资料失败:', error)
        // 可以在这里添加错误上报
      },
    },
  )

  /** 乐观更新状态 */
  const optimisticSettings = useOptimisticState(appSettings.value)

  /** 同步管理器 */
  const syncManager = useSyncManager(async (item) => {
    switch (item.type) {
      case 'user':
        if (item.action === 'update') {
          await userApi.updateProfile(item.data)
        }
        break
      default:
        throw new Error(`未知的同步类型: ${item.type}`)
    }
  })

  // ==================== 计算属性 ====================

  /** 是否已登录 */
  const isLoggedIn = computed(() =>
    loginState.value.isLoggedIn && userInfo.value !== null,
  )

  /** 是否正在登录 */
  const isLogging = computed(() => loginState.value.isLogging)

  /** 用户头像 */
  const userAvatar = computed(() => userInfo.value?.avatar_url || '')

  /** 用户昵称 */
  const userName = computed(() => userInfo.value?.username || '未登录')

  /** 用户ID */
  const userId = computed(() => userInfo.value?.user_id || '')

  /** 是否有待同步数据 */
  const hasPendingSync = computed(() => syncManager.queue.value.length > 0)

  /** 同步状态 */
  const syncStatus = computed(() => syncManager.status.value)

  // ==================== 存储操作 ====================

  /**
   * 从本地存储加载用户数据
   */
  const loadFromStorage = () => {
    try {
      // 加载用户信息
      const storedUserInfo = uni.getStorageSync(STORAGE_KEYS.USER_INFO)
      if (storedUserInfo) {
        userInfo.value = JSON.parse(storedUserInfo)
      }

      // 加载登录状态
      const storedLoginState = uni.getStorageSync(STORAGE_KEYS.LOGIN_STATE)
      if (storedLoginState) {
        loginState.value = { ...loginState.value, ...JSON.parse(storedLoginState) }
      }

      // 加载应用设置
      const storedSettings = uni.getStorageSync(STORAGE_KEYS.APP_SETTINGS)
      if (storedSettings) {
        const settings = JSON.parse(storedSettings)
        appSettings.value = { ...appSettings.value, ...settings }
        optimisticSettings.data.value = { ...appSettings.value, ...settings }
      }
    }
    catch (error) {
      console.error('加载用户数据失败:', error)
    }
  }

  /**
   * 保存用户信息到本地存储
   */
  const saveUserInfo = () => {
    try {
      if (userInfo.value) {
        uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo.value))
      }
    }
    catch (error) {
      console.error('保存用户信息失败:', error)
    }
  }

  /**
   * 保存登录状态到本地存储
   */
  const saveLoginState = () => {
    try {
      uni.setStorageSync(STORAGE_KEYS.LOGIN_STATE, JSON.stringify(loginState.value))
    }
    catch (error) {
      console.error('保存登录状态失败:', error)
    }
  }

  /**
   * 保存应用设置到本地存储
   */
  const saveAppSettings = () => {
    try {
      uni.setStorageSync(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(appSettings.value))
    }
    catch (error) {
      console.error('保存应用设置失败:', error)
    }
  }

  // ==================== 用户操作 ====================

  /**
   * 设置用户信息
   */
  const setUserInfo = (user: User) => {
    userInfo.value = user
    saveUserInfo()

    // 清除相关缓存
    cacheManager.delete(`user_profile_${user.user_id}`)
  }

  /**
   * 开始登录流程
   */
  const startLogin = () => {
    loginState.value.isLogging = true
    saveLoginState()
  }

  /**
   * 登录成功
   */
  const loginSuccess = (user: User, code?: string) => {
    userInfo.value = user
    loginState.value = {
      isLoggedIn: true,
      isLogging: false,
      code,
    }
    saveUserInfo()
    saveLoginState()
    console.log('用户登录成功:', user.username)
  }

  /**
   * 登录失败
   */
  const loginFailed = (error?: string) => {
    loginState.value = {
      isLoggedIn: false,
      isLogging: false,
    }
    saveLoginState()
    console.error('用户登录失败:', error)
  }

  /**
   * 更新用户信息 (带乐观更新)
   */
  const updateUserInfo = async (updates: Partial<User>) => {
    if (!userInfo.value) {
      throw new Error('用户未登录')
    }

    const userId = userInfo.value.user_id
    const originalUser = { ...userInfo.value }

    try {
      // 乐观更新
      userInfo.value = { ...userInfo.value, ...updates }
      saveUserInfo()

      // 调用 API 更新
      const response = await userApi.updateProfile(updates)
      const updatedUser = response.data

      // 更新本地状态
      userInfo.value = updatedUser
      saveUserInfo()

      // 更新缓存
      cacheManager.set(`user_profile_${userId}`, updatedUser)
    }
    catch (error) {
      // 回滚更新
      userInfo.value = originalUser
      saveUserInfo()

      // 如果在线更新失败，添加到同步队列
      if (appSettings.value.offline_mode) {
        syncManager.enqueue({
          action: 'update',
          type: 'user',
          data: updates,
          max_retries: 3,
        })
      }
      throw error
    }
  }

  /**
   * 微信授权登录 (带重试机制)
   */
  const wxLogin = async (): Promise<boolean> => {
    loginState.value.isLogging = true
    saveLoginState()

    try {
      const result = await withRetry(
        async () => {
          // 获取微信授权码
          const loginRes = await new Promise<any>((resolve, reject) => {
            uni.login({
              provider: 'weixin',
              success: resolve,
              fail: reject,
            })
          })

          // 获取用户信息
          const userRes = await new Promise<any>((resolve, reject) => {
            uni.getUserInfo({
              provider: 'weixin',
              success: resolve,
              fail: reject,
            })
          })

          // 调用登录 API
          const loginRequest: WxLoginRequest = {
            code: loginRes.code,
            nickname: userRes.userInfo.nickName,
            avatarUrl: userRes.userInfo.avatarUrl,
          }

          const response = await authApi.wxLogin(loginRequest)
          return response.data
        },
        {
          maxRetries: 3,
          delay: 1000,
          backoffFactor: 2,
          shouldRetry: (error) => {
            // 只对网络错误重试
            return error.type === ErrorType.NETWORK_ERROR
          },
        },
      )

      // 登录成功
      userInfo.value = result.user
      loginState.value = {
        isLoggedIn: true,
        isLogging: false,
        code: undefined,
      }

      // 保存到本地存储
      saveUserInfo()
      saveLoginState()

      // 设置 API 访问令牌
      apiClient.setAccessToken(result.access_token)

      // 缓存用户资料
      cacheManager.set(`user_profile_${result.user.user_id}`, result.user)

      return true
    }
    catch (error) {
      loginState.value = {
        isLoggedIn: false,
        isLogging: false,
      }
      saveLoginState()

      console.error('微信登录失败:', error)
      return false
    }
  }

  /**
   * 退出登录
   */
  const logout = async () => {
    try {
      // 调用退出登录 API
      await authApi.logout()
    }
    catch (error) {
      console.error('退出登录 API 调用失败:', error)
    }

    // 清除本地状态
    userInfo.value = null
    loginState.value = {
      isLoggedIn: false,
      isLogging: false,
    }

    // 清除存储
    try {
      uni.removeStorageSync(STORAGE_KEYS.USER_INFO)
      uni.removeStorageSync(STORAGE_KEYS.LOGIN_STATE)
    }
    catch (error) {
      console.error('清除用户数据失败:', error)
    }

    // 清除 API 访问令牌
    const { apiClient } = await import('@/services/api')
    apiClient.clearAccessToken()

    // 清除缓存
    cacheManager.clear()

    // 重置异步状态
    profileState.reset()
  }

  // ==================== 设置管理 ====================

  /**
   * 更新应用设置 (防抖)
   */
  const updateSettings = useDebounce(async (updates: Partial<AppSettings>) => {
    appSettings.value = { ...appSettings.value, ...updates }
    optimisticSettings.data.value = appSettings.value
    saveAppSettings()

    // 如果启用自动同步，立即同步
    if (appSettings.value.auto_sync && isLoggedIn.value) {
      await syncManager.sync()
    }
  }, 300)

  /**
   * 重置应用设置
   */
  const resetSettings = () => {
    const defaultSettings: AppSettings = {
      theme: 'light',
      language: 'zh-CN',
      notifications_enabled: true,
      auto_sync: true,
      cache_duration: 30,
      offline_mode: false,
    }

    appSettings.value = defaultSettings
    optimisticSettings.data.value = defaultSettings
    saveAppSettings()
  }

  // ==================== 同步操作 ====================

  /**
   * 手动同步数据
   */
  const syncData = async () => {
    await syncManager.sync()
  }

  /**
   * 重试失败的同步
   */
  const retrySync = async () => {
    await syncManager.retryFailed()
  }

  /**
   * 清除同步队列
   */
  const clearSyncQueue = () => {
    syncManager.clearQueue()
  }

  // ==================== 初始化 ====================

  /**
   * 初始化用户 Store
   */
  const init = () => {
    loadFromStorage()

    // 如果已登录，加载用户资料
    if (isLoggedIn.value) {
      profileState.reload()
    }
  }

  // 返回 store 接口
  return {
    // 基础状态
    userInfo,
    loginState,
    appSettings,

    // 高级状态
    profileState,
    optimisticSettings,
    syncStatus,
    hasPendingSync,

    // 计算属性
    isLoggedIn,
    isLogging,
    userAvatar,
    userName,
    userId,

    // 方法
    setUserInfo,
    updateUserInfo,
    startLogin,
    loginSuccess,
    loginFailed,
    wxLogin,
    logout,
    updateSettings,
    resetSettings,
    syncData,
    retrySync,
    clearSyncQueue,
    init,
  }
})

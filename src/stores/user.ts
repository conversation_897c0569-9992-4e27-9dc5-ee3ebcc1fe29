/**
 * 用户状态管理 Store
 * 管理用户信息、登录状态和会话数据
 */

import type { AppSettings, User, UserLoginState } from '@/types'

import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { STORAGE_KEYS } from '@/types'

export const useUserStore = defineStore('user', () => {
  // ==================== 状态定义 ====================

  /** 用户信息 */
  const userInfo = ref<User | null>(null)

  /** 登录状态 */
  const loginState = ref<UserLoginState>({
    isLoggedIn: false,
    isLogging: false,
  })

  /** 应用设置 */
  const appSettings = ref<AppSettings>({
    theme: 'light',
    language: 'zh-CN',
    notifications_enabled: true,
    auto_sync: true,
    cache_duration: 30, // 30分钟
    offline_mode: false,
  })

  // ==================== 计算属性 ====================

  /** 是否已登录 */
  const isLoggedIn = computed(() => loginState.value.isLoggedIn && userInfo.value !== null)

  /** 是否正在登录 */
  const isLogging = computed(() => loginState.value.isLogging)

  /** 用户头像 */
  const userAvatar = computed(() => userInfo.value?.avatar_url || '')

  /** 用户昵称 */
  const userName = computed(() => userInfo.value?.username || '未登录')

  /** 用户ID */
  const userId = computed(() => userInfo.value?.user_id || '')

  // ==================== 存储操作 ====================

  /**
   * 从本地存储加载用户数据
   */
  const loadFromStorage = () => {
    try {
      // 加载用户信息
      const storedUserInfo = uni.getStorageSync(STORAGE_KEYS.USER_INFO)
      if (storedUserInfo) {
        userInfo.value = JSON.parse(storedUserInfo)
      }

      // 加载登录状态
      const storedLoginState = uni.getStorageSync(STORAGE_KEYS.LOGIN_STATE)
      if (storedLoginState) {
        loginState.value = { ...loginState.value, ...JSON.parse(storedLoginState) }
      }

      // 加载应用设置
      const storedSettings = uni.getStorageSync(STORAGE_KEYS.APP_SETTINGS)
      if (storedSettings) {
        appSettings.value = { ...appSettings.value, ...JSON.parse(storedSettings) }
      }

      console.log('用户数据加载成功')
    }
    catch (error) {
      console.error('加载用户数据失败:', error)
    }
  }

  /**
   * 保存用户信息到本地存储
   */
  const saveUserInfo = () => {
    try {
      if (userInfo.value) {
        uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo.value))
      }
    }
    catch (error) {
      console.error('保存用户信息失败:', error)
    }
  }

  /**
   * 保存登录状态到本地存储
   */
  const saveLoginState = () => {
    try {
      uni.setStorageSync(STORAGE_KEYS.LOGIN_STATE, JSON.stringify(loginState.value))
    }
    catch (error) {
      console.error('保存登录状态失败:', error)
    }
  }

  /**
   * 保存应用设置到本地存储
   */
  const saveAppSettings = () => {
    try {
      uni.setStorageSync(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(appSettings.value))
    }
    catch (error) {
      console.error('保存应用设置失败:', error)
    }
  }

  // ==================== 用户操作 ====================

  /**
   * 设置用户信息
   */
  const setUserInfo = (user: User) => {
    userInfo.value = user
    saveUserInfo()
  }

  /**
   * 更新用户信息
   */
  const updateUserInfo = (updates: Partial<User>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...updates }
      saveUserInfo()
    }
  }

  /**
   * 开始登录流程
   */
  const startLogin = () => {
    loginState.value.isLogging = true
    saveLoginState()
  }

  /**
   * 登录成功
   */
  const loginSuccess = (user: User, code?: string) => {
    userInfo.value = user
    loginState.value = {
      isLoggedIn: true,
      isLogging: false,
      code,
    }
    saveUserInfo()
    saveLoginState()
    console.log('用户登录成功:', user.username)
  }

  /**
   * 登录失败
   */
  const loginFailed = (error?: string) => {
    loginState.value = {
      isLoggedIn: false,
      isLogging: false,
    }
    saveLoginState()
    console.error('用户登录失败:', error)
  }

  /**
   * 退出登录
   */
  const logout = () => {
    userInfo.value = null
    loginState.value = {
      isLoggedIn: false,
      isLogging: false,
    }

    // 清除存储的用户数据
    try {
      uni.removeStorageSync(STORAGE_KEYS.USER_INFO)
      uni.removeStorageSync(STORAGE_KEYS.LOGIN_STATE)
      console.log('用户退出登录成功')
    }
    catch (error) {
      console.error('清除用户数据失败:', error)
    }
  }

  // ==================== 微信授权相关 ====================

  /**
   * 微信授权登录
   */
  const wxLogin = async (): Promise<boolean> => {
    return new Promise((resolve) => {
      startLogin()

      uni.login({
        provider: 'weixin',
        success: (loginRes) => {
          console.log('微信登录成功，code:', loginRes.code)

          // 获取用户信息
          uni.getUserInfo({
            provider: 'weixin',
            success: (userRes) => {
              console.log('获取用户信息成功:', userRes.userInfo)

              // 模拟用户数据 (实际应该调用后端API)
              const mockUser: User = {
                user_id: `user_${Date.now()}`,
                open_id: `openid_${loginRes.code}`,
                username: userRes.userInfo.nickName || '微信用户',
                avatar_url: userRes.userInfo.avatarUrl,
                last_login_time: new Date().toISOString(),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              }

              loginSuccess(mockUser, loginRes.code)
              resolve(true)
            },
            fail: (error) => {
              console.error('获取用户信息失败:', error)
              loginFailed('获取用户信息失败')
              resolve(false)
            },
          })
        },
        fail: (error) => {
          console.error('微信登录失败:', error)
          loginFailed('微信登录失败')
          resolve(false)
        },
      })
    })
  }

  // ==================== 设置管理 ====================

  /**
   * 更新应用设置
   */
  const updateSettings = (updates: Partial<AppSettings>) => {
    appSettings.value = { ...appSettings.value, ...updates }
    saveAppSettings()
  }

  /**
   * 重置应用设置
   */
  const resetSettings = () => {
    appSettings.value = {
      theme: 'light',
      language: 'zh-CN',
      notifications_enabled: true,
      auto_sync: true,
      cache_duration: 30,
      offline_mode: false,
    }
    saveAppSettings()
  }

  // ==================== 初始化 ====================

  /**
   * 初始化用户 Store
   */
  const init = () => {
    loadFromStorage()
  }

  // 返回 store 接口
  return {
    // 状态
    userInfo,
    loginState,
    appSettings,

    // 计算属性
    isLoggedIn,
    isLogging,
    userAvatar,
    userName,
    userId,

    // 方法
    setUserInfo,
    updateUserInfo,
    startLogin,
    loginSuccess,
    loginFailed,
    logout,
    wxLogin,
    updateSettings,
    resetSettings,
    init,
  }
})

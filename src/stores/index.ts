/**
 * Pinia Stores 统一导出
 */

import { useEmotionsStore } from './emotions'
import { useEnhancedEmotionsStore } from './enhanced-emotions'
import { useEnhancedUserStore } from './enhanced-user'
import { useUserStore } from './user'

// Store 组合式工具
export * from './composables'
// 原始 stores (向后兼容)
export { useCounterStore } from './counter'
export { useEmotionsStore } from './emotions'

export { useEnhancedEmotionsStore } from './enhanced-emotions'
// 增强版 stores (新数据层)
export { useEnhancedUserStore } from './enhanced-user'

export { useUserStore } from './user'

/**
 * Store 配置选项
 */
interface StoreConfig {
  /** 是否使用增强版 stores */
  useEnhanced: boolean
  /** 是否启用数据迁移 */
  enableMigration: boolean
  /** 是否启用自动同步 */
  enableAutoSync: boolean
}

/**
 * 默认配置
 */
const defaultConfig: StoreConfig = {
  useEnhanced: false, // 默认使用增强版
  enableMigration: true,
  enableAutoSync: true,
}

/**
 * 数据迁移工具
 */
function migrateStoreData() {
  try {
    // eslint-disable-next-line no-console
    console.log('开始数据迁移...')

    // 检查是否有旧版本数据需要迁移
    const oldUserData = uni.getStorageSync('user_info')
    const oldEntriesData = uni.getStorageSync('entries')

    if (oldUserData || oldEntriesData) {
      // eslint-disable-next-line no-console
      console.log('发现旧版本数据，开始迁移')

      // 这里可以添加具体的数据迁移逻辑
      // 例如：格式转换、字段映射等

      // 标记迁移完成
      uni.setStorageSync('data_migration_completed', true)
      // eslint-disable-next-line no-console
      console.log('数据迁移完成')
    }
  }
  catch (error) {
    // eslint-disable-next-line no-console
    console.error('数据迁移失败:', error)
  }
}

/**
 * 初始化所有 stores
 * 在应用启动时调用
 */
export async function initStores(config: Partial<StoreConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config }

  // eslint-disable-next-line no-console
  console.log('初始化 Stores，配置:', finalConfig)

  // 数据迁移
  if (finalConfig.enableMigration) {
    migrateStoreData()
  }

  if (finalConfig.useEnhanced) {
    // 使用增强版 stores
    const userStore = useEnhancedUserStore()
    const emotionsStore = useEnhancedEmotionsStore()

    // 初始化各个 store
    userStore.init()
    emotionsStore.init()

    // 设置自动同步
    if (finalConfig.enableAutoSync) {
      // 每5分钟自动同步一次
      setInterval(() => {
        if (userStore.isLoggedIn && userStore.hasPendingSync) {
          // eslint-disable-next-line no-console
          userStore.syncData().catch(console.error)
        }
        if (emotionsStore.hasPendingSync) {
          // eslint-disable-next-line no-console
          emotionsStore.syncData().catch(console.error)
        }
      }, 5 * 60 * 1000)
    }

    // eslint-disable-next-line no-console
    console.log('增强版 Stores 初始化完成')
  }
  else {
    // 使用原始 stores (向后兼容)
    const emotionsStore = useEmotionsStore()
    const userStore = useUserStore()

    // 初始化各个 store
    userStore.init()
    emotionsStore.init()

    // eslint-disable-next-line no-console
    console.log('原始 Stores 初始化完成')
  }
}

/**
 * 获取当前使用的 stores
 * 根据配置返回对应的 store 实例
 */
export async function getCurrentStores(useEnhanced = true) {
  if (useEnhanced) {
    const { useEnhancedUserStore } = await import('./enhanced-user')
    const { useEnhancedEmotionsStore } = await import('./enhanced-emotions')

    return {
      userStore: useEnhancedUserStore(),
      emotionsStore: useEnhancedEmotionsStore(),
    }
  }
  else {
    return {
      userStore: useUserStore(),
      emotionsStore: useEmotionsStore(),
    }
  }
}

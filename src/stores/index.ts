/**
 * Pinia Stores 统一导出
 */

import { useEmotionsStore } from './emotions'
import { useUserStore } from './user'

// Store 组合式工具
export * from './composables'
// 主要 stores
export { useCounterStore } from './counter'
export { useEmotionsStore } from './emotions'
export { useUserStore } from './user'

/**
 * Store 配置选项
 */
interface StoreConfig {
  /** 是否启用数据迁移 */
  enableMigration: boolean
  /** 是否启用自动同步 */
  enableAutoSync: boolean
}

/**
 * 默认配置
 */
const defaultConfig: StoreConfig = {
  enableMigration: true,
  enableAutoSync: true,
}

/**
 * 数据迁移工具
 */
function migrateStoreData() {
  try {
    // eslint-disable-next-line no-console
    console.log('开始数据迁移...')

    // 检查是否有旧版本数据需要迁移
    const oldUserData = uni.getStorageSync('user_info')
    const oldEntriesData = uni.getStorageSync('entries')

    if (oldUserData || oldEntriesData) {
      // eslint-disable-next-line no-console
      console.log('发现旧版本数据，开始迁移')

      // 这里可以添加具体的数据迁移逻辑
      // 例如：格式转换、字段映射等

      // 标记迁移完成
      uni.setStorageSync('data_migration_completed', true)
      // eslint-disable-next-line no-console
      console.log('数据迁移完成')
    }
  }
  catch (error) {
    // eslint-disable-next-line no-console
    console.error('数据迁移失败:', error)
  }
}

/**
 * 初始化所有 stores
 * 在应用启动时调用
 */
export async function initStores(config: Partial<StoreConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config }

  // eslint-disable-next-line no-console
  console.log('初始化 Stores，配置:', finalConfig)

  // 数据迁移
  if (finalConfig.enableMigration) {
    migrateStoreData()
  }

  // 初始化 stores
  const emotionsStore = useEmotionsStore()
  const userStore = useUserStore()

  // 初始化各个 store
  userStore.init()
  emotionsStore.init()

  // 注意：自动同步功能需要在增强版 stores 中使用
  // 当前使用的是基础版本，不包含同步功能

  // eslint-disable-next-line no-console
  console.log('Stores 初始化完成')
}

/**
 * 获取当前使用的 stores
 * 返回主要的 store 实例
 */
export function getCurrentStores() {
  return {
    userStore: useUserStore(),
    emotionsStore: useEmotionsStore(),
  }
}

<script setup lang="ts">
import { computed, ref } from 'vue'
import WeChatLoginButton from '@/components/WeChatLoginButton.vue'
import { api } from '@/services/api'
import { useEnhancedUserStore } from '@/stores/enhanced-user'

// Store
const userStore = useEnhancedUserStore()

// 响应式状态
const isRefreshing = ref(false)
const isTesting = ref(false)
const isLoggingOut = ref(false)
const testResults = ref<Array<{
  action: string
  success: boolean
  message: string
  timestamp: string
}>>([])

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)
const isLogging = computed(() => userStore.isLogging)
const userInfo = computed(() => userStore.userInfo)

// 添加测试结果
function addTestResult(action: string, success: boolean, message: string) {
  testResults.value.unshift({
    action,
    success,
    message,
    timestamp: new Date().toLocaleTimeString(),
  })

  // 只保留最近10条结果
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

// 登录成功处理
function handleLoginSuccess() {
  addTestResult('微信登录', true, '登录成功')
  uni.showToast({
    title: '登录成功',
    icon: 'success',
  })
}

// 登录失败处理
function handleLoginError(error: string) {
  addTestResult('微信登录', false, error)
  uni.showToast({
    title: '登录失败',
    icon: 'error',
  })
}

// 刷新用户信息
async function refreshUserInfo() {
  try {
    isRefreshing.value = true
    await userStore.refreshProfile()
    addTestResult('刷新用户信息', true, '用户信息已更新')
    uni.showToast({
      title: '刷新成功',
      icon: 'success',
    })
  }
  catch (error: any) {
    addTestResult('刷新用户信息', false, error.message || '刷新失败')
    uni.showToast({
      title: '刷新失败',
      icon: 'error',
    })
  }
  finally {
    isRefreshing.value = false
  }
}

// 测试API调用
async function testApiCall() {
  try {
    isTesting.value = true

    // 测试获取用户配置
    const response = await api.user.getProfile()

    if (response.success) {
      addTestResult('API测试', true, '用户配置获取成功')
      uni.showToast({
        title: 'API测试成功',
        icon: 'success',
      })
    }
    else {
      throw new Error(response.message || 'API调用失败')
    }
  }
  catch (error: any) {
    addTestResult('API测试', false, error.message || 'API调用失败')
    uni.showToast({
      title: 'API测试失败',
      icon: 'error',
    })
  }
  finally {
    isTesting.value = false
  }
}

// 退出登录
async function handleLogout() {
  try {
    isLoggingOut.value = true

    const result = await new Promise<boolean>((resolve) => {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: res => resolve(res.confirm),
        fail: () => resolve(false),
      })
    })

    if (!result) { return }

    await userStore.logout()
    addTestResult('退出登录', true, '已成功退出登录')

    uni.showToast({
      title: '已退出登录',
      icon: 'success',
    })
  }
  catch (error: any) {
    addTestResult('退出登录', false, error.message || '退出失败')
    uni.showToast({
      title: '退出失败',
      icon: 'error',
    })
  }
  finally {
    isLoggingOut.value = false
  }
}

// 获取当前环境
function getCurrentEnvironment() {
  // #ifdef MP-WEIXIN
  return '微信小程序'
  // #endif

  // #ifdef H5
  return 'H5'
  // #endif

  // #ifdef APP-PLUS
  return 'APP'
  // #endif

  return '未知环境'
}

// 获取API基础URL
function getApiBaseUrl() {
  return process.env.NODE_ENV === 'development' ? 'Mock API' : 'Production API'
}

// 获取访问令牌
function getAccessToken() {
  return userStore.accessToken
}

// 页面加载时初始化
onMounted(() => {
  userStore.init()
  addTestResult('页面初始化', true, '测试页面已加载')
})
</script>

<template>
  <view class="test-login-page p-4">
    <view class="header mb-6">
      <text class="mb-2 text-2xl font-bold text-gray-800">WeChat Login Test</text>
      <text class="text-gray-600">测试微信登录功能</text>
    </view>

    <!-- 登录状态显示 -->
    <view class="status-card mb-6 rounded-lg bg-white p-4 shadow-sm">
      <text class="mb-3 text-lg font-semibold">登录状态</text>

      <view class="status-info space-y-2">
        <view class="flex items-center">
          <text class="w-20 text-gray-600">状态:</text>
          <text :class="isLoggedIn ? 'text-green-600' : 'text-red-600'">
            {{ isLoggedIn ? '已登录' : '未登录' }}
          </text>
        </view>

        <view v-if="isLoggedIn" class="flex items-center">
          <text class="w-20 text-gray-600">用户ID:</text>
          <text class="text-gray-800">{{ userInfo?.user_id || 'N/A' }}</text>
        </view>

        <view v-if="isLoggedIn" class="flex items-center">
          <text class="w-20 text-gray-600">用户名:</text>
          <text class="text-gray-800">{{ userInfo?.username || 'N/A' }}</text>
        </view>

        <view v-if="isLoggedIn" class="flex items-center">
          <text class="w-20 text-gray-600">OpenID:</text>
          <text class="text-xs text-gray-800">{{ userInfo?.open_id || 'N/A' }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions space-y-4">
      <!-- 微信登录按钮 -->
      <WeChatLoginButton
        v-if="!isLoggedIn"
        :loading="isLogging"
        @login-success="handleLoginSuccess"
        @login-error="handleLoginError"
      />

      <!-- 已登录时的操作 -->
      <view v-if="isLoggedIn" class="logged-in-actions space-y-3">
        <button
          class="w-full rounded-lg bg-blue-500 p-3 font-medium text-white"
          :disabled="isRefreshing"
          @click="refreshUserInfo"
        >
          {{ isRefreshing ? '刷新中...' : '刷新用户信息' }}
        </button>

        <button
          class="w-full rounded-lg bg-green-500 p-3 font-medium text-white"
          :disabled="isTesting"
          @click="testApiCall"
        >
          {{ isTesting ? '测试中...' : '测试API调用' }}
        </button>

        <button
          class="w-full rounded-lg bg-red-500 p-3 font-medium text-white"
          :disabled="isLoggingOut"
          @click="handleLogout"
        >
          {{ isLoggingOut ? '退出中...' : '退出登录' }}
        </button>
      </view>
    </view>

    <!-- 测试结果 -->
    <view v-if="testResults.length > 0" class="test-results mt-6">
      <text class="mb-3 text-lg font-semibold">测试结果</text>
      <view class="results-list space-y-2">
        <view
          v-for="(result, index) in testResults"
          :key="index"
          class="result-item rounded-lg p-3"
          :class="result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'"
        >
          <view class="mb-1 flex items-center justify-between">
            <text class="font-medium" :class="result.success ? 'text-green-800' : 'text-red-800'">
              {{ result.action }}
            </text>
            <text class="text-xs text-gray-500">{{ result.timestamp }}</text>
          </view>
          <text class="text-sm" :class="result.success ? 'text-green-600' : 'text-red-600'">
            {{ result.message }}
          </text>
        </view>
      </view>
    </view>

    <!-- 调试信息 -->
    <view class="debug-info mt-6 rounded-lg bg-gray-100 p-4">
      <text class="mb-2 text-sm font-semibold">调试信息</text>
      <text class="text-xs leading-relaxed text-gray-600">
        当前环境: {{ getCurrentEnvironment() }}
        <br>
        API Base URL: {{ getApiBaseUrl() }}
        <br>
        Token: {{ getAccessToken() ? '已设置' : '未设置' }}
      </text>
    </view>
  </view>
</template>

<style scoped>
.test-login-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.status-card,
.result-item {
  transition: all 0.2s ease;
}

.result-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

button:active {
  transform: scale(0.98);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>

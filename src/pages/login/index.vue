<template>
  <view class="login-page min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col">
    <!-- 顶部装饰 -->
    <view class="flex-1 flex flex-col justify-center items-center px-8">
      <!-- Logo 区域 -->
      <view class="mb-12 text-center">
        <view class="w-24 h-24 mx-auto mb-6 bg-blue-500 rounded-full flex items-center justify-center">
          <text class="text-white text-4xl">💭</text>
        </view>
        <text class="text-3xl font-bold text-gray-800 mb-2">今日碎语</text>
        <text class="text-gray-600 text-base">记录每一刻的情绪变化</text>
      </view>

      <!-- 登录卡片 -->
      <view class="w-full max-w-sm bg-white rounded-2xl shadow-lg p-8">
        <view class="text-center mb-8">
          <text class="text-xl font-semibold text-gray-800 mb-2">欢迎使用</text>
          <text class="text-gray-600 text-sm">请使用微信授权登录</text>
        </view>

        <!-- 微信登录按钮 -->
        <WeChatLoginButton 
          :loading="isLogging"
          @login-success="handleLoginSuccess"
          @login-error="handleLoginError"
        />

        <!-- 登录状态提示 -->
        <view v-if="loginError" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <text class="text-red-600 text-sm">{{ loginError }}</text>
        </view>

        <!-- 功能介绍 -->
        <view class="mt-8 space-y-3">
          <view class="flex items-center text-gray-600 text-sm">
            <text class="w-2 h-2 bg-blue-500 rounded-full mr-3"></text>
            <text>记录每日情绪变化</text>
          </view>
          <view class="flex items-center text-gray-600 text-sm">
            <text class="w-2 h-2 bg-blue-500 rounded-full mr-3"></text>
            <text>分析情绪趋势</text>
          </view>
          <view class="flex items-center text-gray-600 text-sm">
            <text class="w-2 h-2 bg-blue-500 rounded-full mr-3"></text>
            <text>生成情绪日记</text>
          </view>
        </view>
      </view>

      <!-- 底部说明 -->
      <view class="mt-8 text-center">
        <text class="text-gray-500 text-xs">
          登录即表示同意《用户协议》和《隐私政策》
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useEnhancedUserStore } from '@/stores/enhanced-user'
import WeChatLoginButton from '@/components/WeChatLoginButton.vue'

const userStore = useEnhancedUserStore()

// 响应式状态
const loginError = ref('')
const isLogging = computed(() => userStore.isLogging)

// 登录成功处理
const handleLoginSuccess = () => {
  loginError.value = ''
  
  // 显示成功提示
  uni.showToast({
    title: '登录成功',
    icon: 'success',
    duration: 2000
  })

  // 延迟跳转到主页
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/record/index'
    })
  }, 1500)
}

// 登录失败处理
const handleLoginError = (error: string) => {
  loginError.value = error
  
  uni.showToast({
    title: '登录失败',
    icon: 'error',
    duration: 2000
  })
}

// 页面加载时检查登录状态
onMounted(() => {
  // 如果已经登录，直接跳转到主页
  if (userStore.isLoggedIn) {
    uni.reLaunch({
      url: '/pages/record/index'
    })
  }
})
</script>

<style scoped>
.login-page {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%);
}

/* 动画效果 */
.login-page > view {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮悬停效果 */
.login-page button:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}
</style>

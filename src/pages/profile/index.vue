<script setup lang="ts">
import { computed, ref } from 'vue'
import UserProfile from '@/components/UserProfile.vue'
import { useEnhancedEmotionsStore } from '@/stores/enhanced-emotions'
import { useEnhancedUserStore } from '@/stores/enhanced-user'

// Store
const userStore = useEnhancedUserStore()
const emotionsStore = useEnhancedEmotionsStore()

// 响应式数据
const showSettingsModal = ref(false)
const themeOptions = ['浅色', '深色', '跟随系统']
const themeIndex = ref(0)

// 计算属性
const totalEntries = computed(() => emotionsStore.entries.length)

const thisMonthEntries = computed(() => {
  const now = new Date()
  const currentMonth = now.getMonth()
  const currentYear = now.getFullYear()

  return emotionsStore.entries.filter((entry) => {
    const entryDate = new Date(entry.date)
    return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear
  }).length
})

const continuousDays = computed(() => {
  // 简化的连续天数计算
  const entries = emotionsStore.entries
  if (entries.length === 0) { return 0 }

  const today = new Date()
  let days = 0

  for (let i = 0; i < 30; i++) {
    const checkDate = new Date(today)
    checkDate.setDate(today.getDate() - i)

    const hasEntry = entries.some((entry) => {
      const entryDate = new Date(entry.date)
      return entryDate.toDateString() === checkDate.toDateString()
    })

    if (hasEntry) {
      days++
    }
    else if (i > 0) {
      break
    }
  }

  return days
})

// 方法
async function handleLogin() {
  try {
    const success = await userStore.wxLogin()
    if (success) {
      uni.showToast({
        title: '登录成功',
        icon: 'success',
      })
    }
    else {
      uni.showToast({
        title: '登录失败',
        icon: 'none',
      })
    }
  }
  catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败',
      icon: 'none',
    })
  }
}

function handleLogout() {
  uni.showModal({
    title: '确认退出',
    content: '退出登录后，本地数据将被清除，确定要退出吗？',
    confirmColor: '#FA5151',
    success: (res) => {
      if (res.confirm) {
        userStore.logout()
        uni.showToast({
          title: '已退出登录',
          icon: 'success',
        })
      }
    },
  })
}

function changeAvatar() {
  if (!userStore.isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none',
    })
    return
  }

  uni.showToast({
    title: '头像更换功能开发中',
    icon: 'none',
  })
}

function goToCustomEmotions() {
  uni.showToast({
    title: '自定义情绪库功能开发中',
    icon: 'none',
  })
}

function goToDataExport() {
  if (totalEntries.value === 0) {
    uni.showToast({
      title: '暂无数据可导出',
      icon: 'none',
    })
    return
  }

  uni.showToast({
    title: '数据导出功能开发中',
    icon: 'none',
  })
}

function goToSettings() {
  showSettingsModal.value = true
}

function hideSettings() {
  showSettingsModal.value = false
}

function goToHelp() {
  uni.showModal({
    title: '帮助中心',
    content: '今日碎语是一款简单易用的情绪记录应用。\n\n主要功能：\n• 快速记录情绪状态\n• 分析情绪变化趋势\n• 查看历史记录\n\n如有问题，请联系客服。',
    showCancel: false,
  })
}

function goToAbout() {
  uni.showModal({
    title: '关于今日碎语',
    content: '今日碎语 v1.0.0\n\n一款专注于情绪健康的记录应用，帮助你更好地了解自己的情绪变化。\n\n© 2024 今日碎语团队',
    showCancel: false,
  })
}

function formatLastLogin() {
  if (!userStore.userInfo?.last_login_time) { return '未知' }

  const date = new Date(userStore.userInfo.last_login_time)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

function showVersionInfo() {
  // 彩蛋动画
  uni.showToast({
    title: '🎉 感谢使用今日碎语！',
    icon: 'none',
    duration: 2000,
  })
}

function changeTheme(e: any) {
  themeIndex.value = e.detail.value
  const themes = ['light', 'dark', 'auto'] as const
  userStore.updateSettings({ theme: themes[e.detail.value] })

  uni.showToast({
    title: '主题已更新',
    icon: 'success',
  })
}

function toggleNotifications(e: any) {
  userStore.updateSettings({ notifications_enabled: e.detail.value })

  uni.showToast({
    title: e.detail.value ? '已开启通知' : '已关闭通知',
    icon: 'success',
  })
}

function toggleAutoSync(e: any) {
  userStore.updateSettings({ auto_sync: e.detail.value })

  uni.showToast({
    title: e.detail.value ? '已开启自动同步' : '已关闭自动同步',
    icon: 'success',
  })
}

// 页面生命周期
onShow(() => {
  // 根据当前设置更新主题索引
  const currentTheme = userStore.appSettings.theme
  const themeMap = { light: 0, dark: 1, auto: 2 }
  themeIndex.value = themeMap[currentTheme] || 0
})
</script>

<template>
  <view class="profile-page min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <view class="page-header bg-white shadow-sm">
      <view class="header-content px-4 py-6">
        <text class="text-2xl font-bold text-gray-800">个人中心</text>
        <text class="mt-1 text-sm text-gray-600">管理您的账户和设置</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content px-4 py-6">
      <!-- 用户资料卡片 -->
      <view class="profile-card mb-6 rounded-xl bg-white p-6 shadow-sm">
        <UserProfile />
      </view>

      <!-- 功能入口 -->
      <view class="function-list">
        <view class="function-item" @click="goToCustomEmotions">
          <view class="function-icon">
            <text class="icon">🎨</text>
          </view>
          <text class="function-text">自定义情绪库</text>
          <view class="function-arrow">
            <text>></text>
          </view>
        </view>

        <view class="function-item" @click="goToDataExport">
          <view class="function-icon">
            <text class="icon">📊</text>
          </view>
          <text class="function-text">数据导出</text>
          <view class="function-arrow">
            <text>></text>
          </view>
        </view>

        <view class="function-item" @click="goToSettings">
          <view class="function-icon">
            <text class="icon">⚙️</text>
          </view>
          <text class="function-text">应用设置</text>
          <view class="function-arrow">
            <text>></text>
          </view>
        </view>

        <view class="function-item" @click="goToHelp">
          <view class="function-icon">
            <text class="icon">❓</text>
          </view>
          <text class="function-text">帮助中心</text>
          <view class="function-arrow">
            <text>></text>
          </view>
        </view>

        <view class="function-item" @click="goToAbout">
          <view class="function-icon">
            <text class="icon">ℹ️</text>
          </view>
          <text class="function-text">关于我们</text>
          <view class="function-arrow">
            <text>></text>
          </view>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="stats-section">
        <view class="stats-title">
          我的记录
        </view>
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{ totalEntries }}</text>
            <text class="stat-label">总记录数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ continuousDays }}</text>
            <text class="stat-label">连续天数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ thisMonthEntries }}</text>
            <text class="stat-label">本月记录</text>
          </view>
        </view>
      </view>

      <!-- 底部区域 -->
      <view class="footer-section">
        <view class="version-info" @click="showVersionInfo">
          <text>版本 1.0.0</text>
        </view>

        <view v-if="userStore.isLoggedIn" class="logout-btn" @click="handleLogout">
          <text>退出登录</text>
        </view>
      </view>

      <!-- 设置弹窗 -->
      <view v-if="showSettingsModal" class="settings-modal" @click="hideSettings">
        <view class="settings-content" @click.stop>
          <view class="settings-header">
            <text class="settings-title">应用设置</text>
            <view class="close-btn" @click="hideSettings">
              <text>✕</text>
            </view>
          </view>

          <view class="settings-list">
            <view class="setting-item">
              <text class="setting-label">主题模式</text>
              <view class="setting-control">
                <picker
                  :value="themeIndex"
                  :range="themeOptions"
                  @change="changeTheme"
                >
                  <text class="picker-text">{{ themeOptions[themeIndex] }}</text>
                </picker>
              </view>
            </view>

            <view class="setting-item">
              <text class="setting-label">通知提醒</text>
              <view class="setting-control">
                <switch
                  :checked="userStore.appSettings.notifications_enabled"
                  @change="toggleNotifications"
                />
              </view>
            </view>

            <view class="setting-item">
              <text class="setting-label">自动同步</text>
              <view class="setting-control">
                <switch
                  :checked="userStore.appSettings.auto_sync"
                  @change="toggleAutoSync"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background: #f4f1de;
  padding: 0 32rpx;
}

.user-info-section {
  background: linear-gradient(135deg, #8ecae6 0%, #219ebc 100%);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin: 40rpx 0;
  text-align: center;
  color: white;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  margin: 0 auto 32rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;

  .avatar-text {
    font-size: 64rpx;
    font-weight: 600;
    color: white;
  }
}

.user-details {
  .user-name {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
  }

  .last-login {
    display: block;
    font-size: 24rpx;
    opacity: 0.8;
  }
}

.login-prompt {
  margin-top: 32rpx;
}

.login-btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background: white;
  color: #8ecae6;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.function-list {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .function-icon {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;

    .icon {
      font-size: 32rpx;
    }
  }

  .function-text {
    flex: 1;
    font-size: 28rpx;
    color: #2a2a2a;
  }

  .function-arrow {
    text {
      font-size: 24rpx;
      color: #cccccc;
    }
  }
}

.stats-section {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2a2a2a;
  margin-bottom: 32rpx;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
}

.stat-item {
  text-align: center;

  .stat-number {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    color: #8ecae6;
    margin-bottom: 8rpx;
  }

  .stat-label {
    display: block;
    font-size: 24rpx;
    color: #888888;
  }
}

.footer-section {
  text-align: center;
  padding: 40rpx 0;
}

.version-info {
  margin-bottom: 32rpx;

  text {
    font-size: 24rpx;
    color: #888888;
  }
}

.logout-btn {
  padding: 20rpx 40rpx;
  background: #fa5151;
  color: white;
  border-radius: 16rpx;
  display: inline-block;

  text {
    font-size: 28rpx;
  }
}

// 设置弹窗
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-content {
  background: white;
  border-radius: 16rpx;
  margin: 32rpx;
  max-height: 70vh;
  overflow: hidden;
  width: 600rpx;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #eaeaea;
}

.settings-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2a2a2a;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  text {
    font-size: 32rpx;
    color: #888888;
  }
}

.settings-list {
  padding: 16rpx 0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;

  .setting-label {
    font-size: 28rpx;
    color: #2a2a2a;
  }

  .setting-control {
    .picker-text {
      font-size: 28rpx;
      color: #8ecae6;
    }
  }
}
</style>

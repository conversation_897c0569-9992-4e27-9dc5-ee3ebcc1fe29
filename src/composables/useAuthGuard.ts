import { computed, watch } from 'vue'
import { useEnhancedUserStore } from '@/stores/enhanced-user'

/**
 * 认证守卫 Composable
 * 用于保护需要登录的页面
 */
export function useAuthGuard(options: {
  /** 是否立即检查认证状态 */
  immediate?: boolean
  /** 未登录时的重定向页面 */
  redirectTo?: string
  /** 是否显示登录提示 */
  showLoginPrompt?: boolean
} = {}) {
  const {
    immediate = true,
    redirectTo = '/pages/login/index',
    showLoginPrompt = true
  } = options

  const userStore = useEnhancedUserStore()

  // 计算属性
  const isLoggedIn = computed(() => userStore.isLoggedIn)
  const isLoading = computed(() => userStore.isLogging)

  /**
   * 检查认证状态
   */
  const checkAuth = () => {
    if (!isLoggedIn.value && !isLoading.value) {
      if (showLoginPrompt) {
        uni.showModal({
          title: '需要登录',
          content: '请先登录以使用此功能',
          confirmText: '去登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              redirectToLogin()
            } else {
              // 用户取消，返回上一页或首页
              const pages = getCurrentPages()
              if (pages.length > 1) {
                uni.navigateBack()
              } else {
                uni.reLaunch({
                  url: '/pages/record/index'
                })
              }
            }
          }
        })
      } else {
        redirectToLogin()
      }
      return false
    }
    return true
  }

  /**
   * 重定向到登录页面
   */
  const redirectToLogin = () => {
    uni.reLaunch({
      url: redirectTo
    })
  }

  /**
   * 要求用户登录
   */
  const requireAuth = () => {
    if (!checkAuth()) {
      throw new Error('用户未登录')
    }
  }

  /**
   * 等待用户登录
   */
  const waitForAuth = (): Promise<boolean> => {
    return new Promise((resolve) => {
      if (isLoggedIn.value) {
        resolve(true)
        return
      }

      // 监听登录状态变化
      const unwatch = watch(isLoggedIn, (newValue) => {
        if (newValue) {
          unwatch()
          resolve(true)
        }
      })

      // 检查认证状态
      if (!checkAuth()) {
        unwatch()
        resolve(false)
      }
    })
  }

  /**
   * 自动初始化认证检查
   */
  if (immediate) {
    // 在页面加载时检查认证状态
    onMounted(() => {
      checkAuth()
    })

    // 监听登录状态变化
    watch(isLoggedIn, (newValue) => {
      if (!newValue) {
        checkAuth()
      }
    })
  }

  return {
    isLoggedIn,
    isLoading,
    checkAuth,
    requireAuth,
    waitForAuth,
    redirectToLogin
  }
}

/**
 * 页面级认证守卫
 * 用于在页面组件中快速设置认证保护
 */
export function usePageAuthGuard(options?: Parameters<typeof useAuthGuard>[0]) {
  const authGuard = useAuthGuard({
    immediate: true,
    showLoginPrompt: true,
    ...options
  })

  // 页面显示时检查认证状态
  onShow(() => {
    authGuard.checkAuth()
  })

  return authGuard
}

/**
 * 功能级认证守卫
 * 用于保护特定功能或操作
 */
export function useFeatureAuthGuard() {
  const authGuard = useAuthGuard({
    immediate: false,
    showLoginPrompt: true
  })

  /**
   * 执行需要认证的操作
   */
  const executeWithAuth = async <T>(
    operation: () => Promise<T> | T,
    options?: {
      loginPrompt?: string
      onAuthFailed?: () => void
    }
  ): Promise<T | null> => {
    try {
      authGuard.requireAuth()
      return await operation()
    } catch (error) {
      if (error instanceof Error && error.message === '用户未登录') {
        if (options?.loginPrompt) {
          uni.showToast({
            title: options.loginPrompt,
            icon: 'none'
          })
        }
        
        const authSuccess = await authGuard.waitForAuth()
        if (authSuccess) {
          return await operation()
        } else {
          options?.onAuthFailed?.()
          return null
        }
      }
      throw error
    }
  }

  return {
    ...authGuard,
    executeWithAuth
  }
}

/**
 * 微信登录助手
 * 提供便捷的微信登录方法
 */
export function useWeChatLogin() {
  const userStore = useEnhancedUserStore()

  /**
   * 执行微信登录
   */
  const login = async (options?: {
    showLoading?: boolean
    loadingText?: string
    successMessage?: string
    onSuccess?: () => void
    onError?: (error: string) => void
  }) => {
    const {
      showLoading = true,
      loadingText = '登录中...',
      successMessage = '登录成功',
      onSuccess,
      onError
    } = options || {}

    try {
      if (showLoading) {
        uni.showLoading({
          title: loadingText,
          mask: true
        })
      }

      const success = await userStore.wxLogin()

      if (success) {
        if (showLoading) {
          uni.hideLoading()
        }
        
        uni.showToast({
          title: successMessage,
          icon: 'success'
        })

        onSuccess?.()
        return true
      } else {
        throw new Error('登录失败')
      }
    } catch (error: any) {
      if (showLoading) {
        uni.hideLoading()
      }

      const errorMessage = error.message || '登录失败，请重试'
      
      uni.showToast({
        title: errorMessage,
        icon: 'error'
      })

      onError?.(errorMessage)
      return false
    }
  }

  /**
   * 退出登录
   */
  const logout = async (options?: {
    showConfirm?: boolean
    confirmTitle?: string
    confirmContent?: string
    successMessage?: string
    onSuccess?: () => void
  }) => {
    const {
      showConfirm = true,
      confirmTitle = '确认退出',
      confirmContent = '确定要退出登录吗？',
      successMessage = '已退出登录',
      onSuccess
    } = options || {}

    try {
      if (showConfirm) {
        const result = await new Promise<boolean>((resolve) => {
          uni.showModal({
            title: confirmTitle,
            content: confirmContent,
            success: (res) => resolve(res.confirm),
            fail: () => resolve(false)
          })
        })

        if (!result) return false
      }

      await userStore.logout()

      uni.showToast({
        title: successMessage,
        icon: 'success'
      })

      onSuccess?.()
      return true
    } catch (error) {
      console.error('退出登录失败:', error)
      uni.showToast({
        title: '退出失败',
        icon: 'error'
      })
      return false
    }
  }

  return {
    login,
    logout,
    isLoggedIn: computed(() => userStore.isLoggedIn),
    isLogging: computed(() => userStore.isLogging),
    userInfo: computed(() => userStore.userInfo)
  }
}

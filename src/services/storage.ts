/**
 * 内存数据存储服务
 * 模拟数据库操作，用于开发和测试环境
 */

import type {
  User,
  Entry,
  Emotion,
  Cause,
  PaginationParams,
  PaginationResponse,
  QueryEntriesRequest,
  CacheItem,
  CacheConfig,
  ValidationResult,
  AppError,
  ErrorType
} from '@/types'

/**
 * 内存存储配置
 */
interface MemoryStorageConfig {
  /** 是否启用持久化到本地存储 */
  enablePersistence: boolean
  /** 自动保存间隔 (毫秒) */
  autoSaveInterval: number
  /** 最大存储条目数 */
  maxEntries: number
  /** 是否启用数据验证 */
  enableValidation: boolean
}

/**
 * 数据表接口
 */
interface DataTable<T> {
  /** 数据存储 */
  data: Map<string | number, T>
  /** 索引 */
  indexes: Map<string, Map<any, Set<string | number>>>
  /** 自增ID计数器 */
  autoIncrement: number
}

/**
 * 内存数据存储类
 */
class MemoryStorage {
  private config: MemoryStorageConfig
  private users: DataTable<User>
  private entries: DataTable<Entry>
  private emotions: DataTable<Emotion>
  private causes: DataTable<Cause>
  private cache: Map<string, CacheItem<any>>
  private saveTimer?: number

  constructor(config: Partial<MemoryStorageConfig> = {}) {
    this.config = {
      enablePersistence: true,
      autoSaveInterval: 5000, // 5秒
      maxEntries: 10000,
      enableValidation: true,
      ...config
    }

    // 初始化数据表
    this.users = this.createTable<User>()
    this.entries = this.createTable<Entry>()
    this.emotions = this.createTable<Emotion>()
    this.causes = this.createTable<Cause>()
    this.cache = new Map()

    // 加载持久化数据
    if (this.config.enablePersistence) {
      this.loadFromPersistence()
      this.startAutoSave()
    }

    // 初始化默认数据
    this.initializeDefaultData()
  }

  /**
   * 创建数据表
   */
  private createTable<T>(): DataTable<T> {
    return {
      data: new Map(),
      indexes: new Map(),
      autoIncrement: 1
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(table: DataTable<any>): number {
    return table.autoIncrement++
  }

  /**
   * 创建索引
   */
  private createIndex<T>(table: DataTable<T>, field: string, value: any, id: string | number) {
    if (!table.indexes.has(field)) {
      table.indexes.set(field, new Map())
    }
    
    const fieldIndex = table.indexes.get(field)!
    if (!fieldIndex.has(value)) {
      fieldIndex.set(value, new Set())
    }
    
    fieldIndex.get(value)!.add(id)
  }

  /**
   * 删除索引
   */
  private removeIndex<T>(table: DataTable<T>, field: string, value: any, id: string | number) {
    const fieldIndex = table.indexes.get(field)
    if (fieldIndex && fieldIndex.has(value)) {
      fieldIndex.get(value)!.delete(id)
      if (fieldIndex.get(value)!.size === 0) {
        fieldIndex.delete(value)
      }
    }
  }

  /**
   * 通过索引查询
   */
  private queryByIndex<T>(table: DataTable<T>, field: string, value: any): T[] {
    const fieldIndex = table.indexes.get(field)
    if (!fieldIndex || !fieldIndex.has(value)) {
      return []
    }

    const ids = fieldIndex.get(value)!
    return Array.from(ids)
      .map(id => table.data.get(id))
      .filter(Boolean) as T[]
  }

  /**
   * 验证数据
   */
  private validateData<T>(data: T, type: string): ValidationResult {
    if (!this.config.enableValidation) {
      return { valid: true, errors: {} }
    }

    const errors: Record<string, string[]> = {}

    // 基础验证逻辑
    switch (type) {
      case 'user':
        const user = data as unknown as User
        if (!user.user_id) errors.user_id = ['用户ID不能为空']
        if (!user.username) errors.username = ['用户名不能为空']
        break
      
      case 'entry':
        const entry = data as unknown as Entry
        if (!entry.user_id) errors.user_id = ['用户ID不能为空']
        if (!entry.emotion_id) errors.emotion_id = ['情绪ID不能为空']
        if (!entry.cause_id) errors.cause_id = ['原因ID不能为空']
        if (entry.intensity < 1 || entry.intensity > 5) {
          errors.intensity = ['情绪强度必须在1-5之间']
        }
        break
    }

    return {
      valid: Object.keys(errors).length === 0,
      errors
    }
  }

  /**
   * 创建应用错误
   */
  private createError(type: ErrorType, message: string, details?: any): AppError {
    const error = new Error(message) as AppError
    error.type = type
    error.code = type
    error.details = details
    error.retryable = type === ErrorType.NETWORK_ERROR
    error.timestamp = new Date().toISOString()
    return error
  }

  // ==================== 用户操作 ====================

  /**
   * 创建用户
   */
  async createUser(userData: Omit<User, 'user_id'>): Promise<User> {
    const validation = this.validateData(userData, 'user')
    if (!validation.valid) {
      throw this.createError(ErrorType.VALIDATION_ERROR, '用户数据验证失败', validation.errors)
    }

    const userId = `user_${this.generateId(this.users)}`
    const user: User = {
      ...userData,
      user_id: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    this.users.data.set(userId, user)
    this.createIndex(this.users, 'open_id', user.open_id, userId)
    
    return user
  }

  /**
   * 根据ID获取用户
   */
  async getUserById(userId: string): Promise<User | null> {
    return this.users.data.get(userId) || null
  }

  /**
   * 根据OpenID获取用户
   */
  async getUserByOpenId(openId: string): Promise<User | null> {
    const users = this.queryByIndex(this.users, 'open_id', openId)
    return users[0] || null
  }

  /**
   * 更新用户
   */
  async updateUser(userId: string, updates: Partial<User>): Promise<User> {
    const existingUser = this.users.data.get(userId)
    if (!existingUser) {
      throw this.createError(ErrorType.CLIENT_ERROR, '用户不存在')
    }

    const updatedUser: User = {
      ...existingUser,
      ...updates,
      user_id: userId, // 确保ID不被修改
      updated_at: new Date().toISOString()
    }

    this.users.data.set(userId, updatedUser)
    return updatedUser
  }

  // ==================== 情绪记录操作 ====================

  /**
   * 创建情绪记录
   */
  async createEntry(entryData: Omit<Entry, 'entry_id' | 'created_at' | 'updated_at'>): Promise<Entry> {
    const validation = this.validateData(entryData, 'entry')
    if (!validation.valid) {
      throw this.createError(ErrorType.VALIDATION_ERROR, '记录数据验证失败', validation.errors)
    }

    // 检查是否超过最大条目数
    if (this.entries.data.size >= this.config.maxEntries) {
      throw this.createError(ErrorType.CLIENT_ERROR, '存储空间已满')
    }

    const entryId = this.generateId(this.entries)
    const entry: Entry = {
      ...entryData,
      entry_id: entryId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    this.entries.data.set(entryId, entry)
    
    // 创建索引
    this.createIndex(this.entries, 'user_id', entry.user_id, entryId)
    this.createIndex(this.entries, 'emotion_id', entry.emotion_id, entryId)
    this.createIndex(this.entries, 'cause_id', entry.cause_id, entryId)
    
    return entry
  }

  /**
   * 查询情绪记录
   */
  async queryEntries(params: QueryEntriesRequest): Promise<PaginationResponse<Entry>> {
    let results = Array.from(this.entries.data.values())

    // 应用筛选条件
    if (params.user_id) {
      results = results.filter(entry => entry.user_id === params.user_id)
    }
    if (params.emotion_id) {
      results = results.filter(entry => entry.emotion_id === params.emotion_id)
    }
    if (params.cause_id) {
      results = results.filter(entry => entry.cause_id === params.cause_id)
    }
    if (params.start_date) {
      results = results.filter(entry => entry.date >= params.start_date!)
    }
    if (params.end_date) {
      results = results.filter(entry => entry.date <= params.end_date!)
    }
    if (params.min_intensity) {
      results = results.filter(entry => entry.intensity >= params.min_intensity!)
    }
    if (params.max_intensity) {
      results = results.filter(entry => entry.intensity <= params.max_intensity!)
    }
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase()
      results = results.filter(entry => 
        entry.notes.toLowerCase().includes(keyword)
      )
    }

    // 排序
    const sortBy = params.sort_by || 'created_at'
    const sortOrder = params.sort_order || 'desc'
    results.sort((a, b) => {
      const aValue = (a as any)[sortBy]
      const bValue = (b as any)[sortBy]
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    // 分页
    const total = results.length
    const page = params.page || 1
    const pageSize = params.page_size || 20
    const offset = (page - 1) * pageSize
    const items = results.slice(offset, offset + pageSize)

    return {
      items,
      total,
      page,
      page_size: pageSize,
      total_pages: Math.ceil(total / pageSize),
      has_next: offset + pageSize < total,
      has_prev: page > 1
    }
  }

  /**
   * 更新情绪记录
   */
  async updateEntry(entryId: number, updates: Partial<Entry>): Promise<Entry> {
    const existingEntry = this.entries.data.get(entryId)
    if (!existingEntry) {
      throw this.createError(ErrorType.CLIENT_ERROR, '记录不存在')
    }

    const updatedEntry: Entry = {
      ...existingEntry,
      ...updates,
      entry_id: entryId, // 确保ID不被修改
      updated_at: new Date().toISOString()
    }

    this.entries.data.set(entryId, updatedEntry)
    return updatedEntry
  }

  /**
   * 删除情绪记录
   */
  async deleteEntry(entryId: number): Promise<boolean> {
    const entry = this.entries.data.get(entryId)
    if (!entry) {
      return false
    }

    // 删除索引
    this.removeIndex(this.entries, 'user_id', entry.user_id, entryId)
    this.removeIndex(this.entries, 'emotion_id', entry.emotion_id, entryId)
    this.removeIndex(this.entries, 'cause_id', entry.cause_id, entryId)

    return this.entries.data.delete(entryId)
  }

  // ==================== 情绪和原因操作 ====================

  /**
   * 获取所有情绪
   */
  async getEmotions(): Promise<Emotion[]> {
    return Array.from(this.emotions.data.values())
      .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
  }

  /**
   * 获取所有原因
   */
  async getCauses(): Promise<Cause[]> {
    return Array.from(this.causes.data.values())
      .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
  }

  /**
   * 创建自定义情绪
   */
  async createEmotion(emotionData: Omit<Emotion, 'emotion_id' | 'created_at' | 'updated_at'>): Promise<Emotion> {
    const emotionId = this.generateId(this.emotions)
    const emotion: Emotion = {
      ...emotionData,
      emotion_id: emotionId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    this.emotions.data.set(emotionId, emotion)
    return emotion
  }

  /**
   * 创建自定义原因
   */
  async createCause(causeData: Omit<Cause, 'cause_id' | 'created_at' | 'updated_at'>): Promise<Cause> {
    const causeId = this.generateId(this.causes)
    const cause: Cause = {
      ...causeData,
      cause_id: causeId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    this.causes.data.set(causeId, cause)
    return cause
  }

  // ==================== 持久化操作 ====================

  /**
   * 保存到本地存储
   */
  private saveToPersistence() {
    if (!this.config.enablePersistence) return

    try {
      const data = {
        users: Array.from(this.users.data.entries()),
        entries: Array.from(this.entries.data.entries()),
        emotions: Array.from(this.emotions.data.entries()),
        causes: Array.from(this.causes.data.entries()),
        autoIncrements: {
          users: this.users.autoIncrement,
          entries: this.entries.autoIncrement,
          emotions: this.emotions.autoIncrement,
          causes: this.causes.autoIncrement
        }
      }

      uni.setStorageSync('memory_storage_data', JSON.stringify(data))
    } catch (error) {
      console.error('保存数据到本地存储失败:', error)
    }
  }

  /**
   * 从本地存储加载
   */
  private loadFromPersistence() {
    try {
      const dataStr = uni.getStorageSync('memory_storage_data')
      if (!dataStr) return

      const data = JSON.parse(dataStr)
      
      // 恢复数据
      this.users.data = new Map(data.users || [])
      this.entries.data = new Map(data.entries || [])
      this.emotions.data = new Map(data.emotions || [])
      this.causes.data = new Map(data.causes || [])

      // 恢复自增计数器
      if (data.autoIncrements) {
        this.users.autoIncrement = data.autoIncrements.users || 1
        this.entries.autoIncrement = data.autoIncrements.entries || 1
        this.emotions.autoIncrement = data.autoIncrements.emotions || 1
        this.causes.autoIncrement = data.autoIncrements.causes || 1
      }

      // 重建索引
      this.rebuildIndexes()
    } catch (error) {
      console.error('从本地存储加载数据失败:', error)
    }
  }

  /**
   * 重建索引
   */
  private rebuildIndexes() {
    // 重建用户索引
    this.users.indexes.clear()
    for (const [id, user] of this.users.data) {
      this.createIndex(this.users, 'open_id', user.open_id, id)
    }

    // 重建记录索引
    this.entries.indexes.clear()
    for (const [id, entry] of this.entries.data) {
      this.createIndex(this.entries, 'user_id', entry.user_id, id)
      this.createIndex(this.entries, 'emotion_id', entry.emotion_id, id)
      this.createIndex(this.entries, 'cause_id', entry.cause_id, id)
    }
  }

  /**
   * 开始自动保存
   */
  private startAutoSave() {
    if (this.saveTimer) {
      clearInterval(this.saveTimer)
    }

    this.saveTimer = setInterval(() => {
      this.saveToPersistence()
    }, this.config.autoSaveInterval) as unknown as number
  }

  /**
   * 停止自动保存
   */
  stopAutoSave() {
    if (this.saveTimer) {
      clearInterval(this.saveTimer)
      this.saveTimer = undefined
    }
  }

  /**
   * 手动保存
   */
  save() {
    this.saveToPersistence()
  }

  /**
   * 清空所有数据
   */
  clear() {
    this.users.data.clear()
    this.entries.data.clear()
    this.emotions.data.clear()
    this.causes.data.clear()
    
    this.users.indexes.clear()
    this.entries.indexes.clear()
    this.emotions.indexes.clear()
    this.causes.indexes.clear()

    this.cache.clear()

    if (this.config.enablePersistence) {
      uni.removeStorageSync('memory_storage_data')
    }
  }

  /**
   * 初始化默认数据
   */
  private initializeDefaultData() {
    // 如果没有情绪数据，初始化默认情绪
    if (this.emotions.data.size === 0) {
      this.seedEmotions()
    }

    // 如果没有原因数据，初始化默认原因
    if (this.causes.data.size === 0) {
      this.seedCauses()
    }
  }

  /**
   * 初始化默认情绪数据
   */
  private async seedEmotions() {
    const defaultEmotions = [
      { emotion_label: '开心', emotion_icon: 'mdi:emoticon-happy-outline', emotion_color: '#FFD93D', is_system: true, user_id: 'system', sort_order: 1 },
      { emotion_label: '平静', emotion_icon: 'mdi:emoticon-neutral-outline', emotion_color: '#8ECAE6', is_system: true, user_id: 'system', sort_order: 2 },
      { emotion_label: '难过', emotion_icon: 'mdi:emoticon-sad-outline', emotion_color: '#6C9BD1', is_system: true, user_id: 'system', sort_order: 3 },
      { emotion_label: '焦虑', emotion_icon: 'mdi:emoticon-confused-outline', emotion_color: '#F4A261', is_system: true, user_id: 'system', sort_order: 4 },
      { emotion_label: '愤怒', emotion_icon: 'mdi:emoticon-angry-outline', emotion_color: '#E76F51', is_system: true, user_id: 'system', sort_order: 5 },
      { emotion_label: '兴奋', emotion_icon: 'mdi:emoticon-excited-outline', emotion_color: '#F72585', is_system: true, user_id: 'system', sort_order: 6 },
      { emotion_label: '疲惫', emotion_icon: 'mdi:emoticon-dead-outline', emotion_color: '#A8DADC', is_system: true, user_id: 'system', sort_order: 7 },
      { emotion_label: '感激', emotion_icon: 'mdi:heart-outline', emotion_color: '#F1FAEE', is_system: true, user_id: 'system', sort_order: 8 },
      { emotion_label: '困惑', emotion_icon: 'mdi:help-circle-outline', emotion_color: '#457B9D', is_system: true, user_id: 'system', sort_order: 9 },
    ]

    for (const emotionData of defaultEmotions) {
      await this.createEmotion(emotionData)
    }
  }

  /**
   * 初始化默认原因数据
   */
  private async seedCauses() {
    const defaultCauses = [
      // 工作相关
      { cause_label: '工作压力', cause_category: '工作', is_system: true, user_id: 'system', sort_order: 1 },
      { cause_label: '项目进展', cause_category: '工作', is_system: true, user_id: 'system', sort_order: 2 },
      { cause_label: '同事关系', cause_category: '工作', is_system: true, user_id: 'system', sort_order: 3 },
      
      // 社交相关
      { cause_label: '朋友聚会', cause_category: '社交', is_system: true, user_id: 'system', sort_order: 1 },
      { cause_label: '家庭关系', cause_category: '社交', is_system: true, user_id: 'system', sort_order: 2 },
      { cause_label: '恋爱关系', cause_category: '社交', is_system: true, user_id: 'system', sort_order: 3 },
      
      // 健康相关
      { cause_label: '身体状况', cause_category: '健康', is_system: true, user_id: 'system', sort_order: 1 },
      { cause_label: '睡眠质量', cause_category: '健康', is_system: true, user_id: 'system', sort_order: 2 },
      { cause_label: '运动锻炼', cause_category: '健康', is_system: true, user_id: 'system', sort_order: 3 },
      
      // 生活相关
      { cause_label: '日常琐事', cause_category: '生活', is_system: true, user_id: 'system', sort_order: 1 },
      { cause_label: '财务状况', cause_category: '生活', is_system: true, user_id: 'system', sort_order: 2 },
      { cause_label: '学习成长', cause_category: '生活', is_system: true, user_id: 'system', sort_order: 3 },
      
      // 其他
      { cause_label: '天气变化', cause_category: '其他', is_system: true, user_id: 'system', sort_order: 1 },
      { cause_label: '突发事件', cause_category: '其他', is_system: true, user_id: 'system', sort_order: 2 },
      { cause_label: '无特定原因', cause_category: '其他', is_system: true, user_id: 'system', sort_order: 3 },
    ]

    for (const causeData of defaultCauses) {
      await this.createCause(causeData)
    }
  }
}

// 创建全局存储实例
export const memoryStorage = new MemoryStorage({
  enablePersistence: true,
  autoSaveInterval: 5000,
  maxEntries: 10000,
  enableValidation: true
})

export default memoryStorage
